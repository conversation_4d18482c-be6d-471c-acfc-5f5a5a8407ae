import React, { useState, useRef, useEffect } from 'react';
import { Camera, Lock, Eye, EyeOff, Loader2, AlertCircle, CheckCircle, User, Shield, Fingerprint, Sparkles, Zap } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const UltraModernLogin: React.FC = () => {
  const { login } = useAuth();
  const [loginMode, setLoginMode] = useState<'face' | 'pin'>('pin');
  const [username, setUsername] = useState('');
  const [pinCode, setPinCode] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [faceDetectionStatus, setFaceDetectionStatus] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Nettoyage automatique des messages
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  // Nettoyage de la caméra
  useEffect(() => {
    return () => {
      deactivateCamera();
    };
  }, []);

  const activateCamera = async () => {
    try {
      setIsLoading(true);
      setFaceDetectionStatus('Activation de la caméra...');
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: 640, 
          height: 480,
          facingMode: 'user'
        } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraActive(true);
        setFaceDetectionStatus('Caméra activée - Positionnez votre visage');
      }
    } catch (error) {
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
      setFaceDetectionStatus(null);
    } finally {
      setIsLoading(false);
    }
  };

  const deactivateCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setCameraActive(false);
    setFaceDetectionStatus(null);
  };

  const authenticateWithFace = async () => {
    setIsLoading(true);
    setFaceDetectionStatus('Analyse biométrique en cours...');
    
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setSuccess('Authentification biométrique réussie !');
      setFaceDetectionStatus('Identité confirmée');
      
      setTimeout(async () => {
        await login('face-id-user', 'face-auth');
      }, 1500);
      
    } catch (error) {
      setError('Échec de l\'authentification biométrique');
      setFaceDetectionStatus('Identité non reconnue');
    } finally {
      setIsLoading(false);
    }
  };

  const authenticateWithPIN = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      if (username === 'directeur.credit' && pinCode === '1234') {
        setSuccess('Authentification réussie !');
        setTimeout(async () => {
          await login(username, pinCode);
        }, 1000);
      } else {
        setError('Identifiants incorrects');
      }
    } catch (error) {
      setError('Erreur de connexion');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
      {/* Photo réelle de banque ultra-moderne en arrière-plan */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`
        }}
      />
      
      {/* Overlay avec effet de flou ultra-moderne */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/70 via-blue-900/80 to-indigo-900/90 backdrop-blur-md"></div>
      
      {/* Effet de particules lumineuses premium */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-blue-400/60 rounded-full animate-pulse shadow-lg"></div>
        <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-white/50 rounded-full animate-ping shadow-lg"></div>
        <div className="absolute bottom-1/4 left-1/3 w-4 h-4 bg-indigo-400/40 rounded-full animate-bounce shadow-lg"></div>
        <div className="absolute top-1/2 right-1/3 w-2 h-2 bg-cyan-400/60 rounded-full animate-pulse shadow-lg"></div>
        <div className="absolute top-3/4 left-1/2 w-3 h-3 bg-white/40 rounded-full animate-ping shadow-lg"></div>
        <div className="absolute top-1/6 right-1/6 w-2 h-2 bg-blue-300/70 rounded-full animate-bounce shadow-lg"></div>
      </div>
      
      {/* Grille de points lumineux premium */}
      <div className="absolute inset-0 opacity-10">
        <div className="grid grid-cols-20 gap-6 h-full w-full p-8">
          {Array.from({ length: 200 }).map((_, i) => (
            <div 
              key={i} 
              className="w-1 h-1 bg-white/30 rounded-full animate-pulse" 
              style={{ animationDelay: `${i * 0.05}s` }}
            />
          ))}
        </div>
      </div>

      {/* Formulaire de connexion centré ultra-moderne */}
      <div className="relative z-10 w-full max-w-md mx-auto px-6">
        
        {/* Logo flottant premium */}
        <div className="text-center mb-10">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 backdrop-blur-2xl rounded-3xl border border-white/20 shadow-2xl mb-6 relative">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl blur-xl"></div>
            <div className="relative w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold text-white mb-3 tracking-tight">TuniBankX</h1>
          <p className="text-white/80 text-xl font-medium">Plateforme Bancaire Révolutionnaire</p>
          <div className="flex items-center justify-center mt-4 space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-300 text-sm font-medium">Système Ultra-Sécurisé</span>
          </div>
        </div>

        {/* Carte de connexion ultra-moderne */}
        <div className="bg-white/5 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/10 p-8 relative overflow-hidden">
          {/* Effets visuels premium */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-cyan-600/20 rounded-full blur-3xl"></div>
          
          {/* En-tête du formulaire premium */}
          <div className="text-center mb-8 relative z-10">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-xl mb-4">
              <Lock className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">Accès Sécurisé</h3>
            <p className="text-white/70">Plateforme Bancaire Professionnelle</p>
          </div>
          
          {/* Indicateur de sécurité premium */}
          <div className="flex items-center justify-center mb-8 relative z-10">
            <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-xl px-6 py-3 rounded-2xl border border-white/20">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-white/90 text-sm font-medium">Chiffrement AES-256</span>
            </div>
          </div>

          {/* Sélecteur de mode ultra-moderne */}
          <div className="flex bg-white/10 backdrop-blur-xl rounded-2xl p-1.5 mb-8 border border-white/20 relative z-10">
            <button
              onClick={() => {
                setLoginMode('face');
                deactivateCamera();
                setError(null);
                setSuccess(null);
              }}
              className={`flex-1 py-4 px-6 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                loginMode === 'face'
                  ? 'bg-white/20 text-white shadow-lg transform scale-105 border border-white/30'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Camera className="w-5 h-5" />
              <span>Biométrie</span>
              {loginMode === 'face' && (
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              )}
            </button>
            <button
              onClick={() => {
                setLoginMode('pin');
                deactivateCamera();
                setError(null);
                setSuccess(null);
              }}
              className={`flex-1 py-4 px-6 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                loginMode === 'pin'
                  ? 'bg-white/20 text-white shadow-lg transform scale-105 border border-white/30'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Lock className="w-5 h-5" />
              <span>Code PIN</span>
              {loginMode === 'pin' && (
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              )}
            </button>
          </div>

          {/* Interface Face ID ultra-moderne */}
          {loginMode === 'face' && (
            <div className="space-y-6 relative z-10">
              <div className="text-center">
                <h3 className="text-xl font-semibold text-white mb-2">Authentification Biométrique</h3>
                <p className="text-white/70 text-sm">Technologie de reconnaissance faciale 3D avancée</p>
              </div>

              {/* Zone caméra ultra-moderne */}
              <div className="relative">
                <div className="w-full h-64 mx-auto bg-white/5 backdrop-blur-xl rounded-2xl border border-white/20 flex items-center justify-center overflow-hidden shadow-2xl">
                  {cameraActive ? (
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="w-full h-full object-cover rounded-2xl"
                    />
                  ) : (
                    <div className="text-center">
                      <div className="w-20 h-20 bg-white/10 backdrop-blur-xl rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20">
                        <Camera className="w-10 h-10 text-white/70" />
                      </div>
                      <p className="text-white/80 text-sm font-medium">Caméra désactivée</p>
                      <p className="text-white/60 text-xs mt-1">Cliquez pour activer la reconnaissance</p>
                    </div>
                  )}
                </div>

                {/* Overlay de détection ultra-moderne */}
                {cameraActive && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-48 h-36 border-2 border-blue-400 rounded-2xl animate-pulse shadow-2xl bg-blue-400/10 backdrop-blur-sm"></div>
                      {/* Coins de cadrage premium */}
                      <div className="absolute -top-2 -left-2 w-8 h-8 border-t-4 border-l-4 border-blue-400 rounded-tl-2xl"></div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 border-t-4 border-r-4 border-blue-400 rounded-tr-2xl"></div>
                      <div className="absolute -bottom-2 -left-2 w-8 h-8 border-b-4 border-l-4 border-blue-400 rounded-bl-2xl"></div>
                      <div className="absolute -bottom-2 -right-2 w-8 h-8 border-b-4 border-r-4 border-blue-400 rounded-br-2xl"></div>
                    </div>
                  </div>
                )}
              </div>

              {/* Statut de détection premium */}
              {faceDetectionStatus && (
                <div className="text-center bg-white/10 backdrop-blur-xl p-4 rounded-2xl border border-white/20">
                  <div className="flex items-center justify-center space-x-3">
                    <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                    <Zap className="w-5 h-5 text-blue-400" />
                    <p className="text-white font-medium">{faceDetectionStatus}</p>
                  </div>
                </div>
              )}

              {/* Boutons Face ID ultra-modernes */}
              <div className="space-y-4">
                {!cameraActive ? (
                  <button
                    onClick={activateCamera}
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 px-6 rounded-2xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 flex items-center justify-center shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1"
                  >
                    {isLoading ? (
                      <Loader2 className="w-6 h-6 animate-spin" />
                    ) : (
                      <>
                        <Camera className="w-6 h-6 mr-3" />
                        Activer la Reconnaissance
                      </>
                    )}
                  </button>
                ) : (
                  <div className="space-y-3">
                    <button
                      onClick={authenticateWithFace}
                      disabled={isLoading}
                      className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-4 px-6 rounded-2xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 disabled:opacity-50 flex items-center justify-center shadow-2xl hover:shadow-green-500/25 transform hover:-translate-y-1"
                    >
                      {isLoading ? (
                        <Loader2 className="w-6 h-6 animate-spin" />
                      ) : (
                        <>
                          <Sparkles className="w-6 h-6 mr-3" />
                          Scanner l'Identité
                        </>
                      )}
                    </button>
                    <button
                      onClick={deactivateCamera}
                      className="w-full bg-white/10 backdrop-blur-xl text-white py-3 px-6 rounded-2xl font-medium hover:bg-white/20 transition-all duration-300 border border-white/20"
                    >
                      Arrêter la Caméra
                    </button>
                  </div>
                )}
              </div>

              {/* Informations de sécurité premium */}
              <div className="bg-green-500/10 backdrop-blur-xl p-4 rounded-2xl border border-green-400/20">
                <div className="flex items-start space-x-3">
                  <Shield className="w-6 h-6 text-green-400 mt-0.5" />
                  <div>
                    <p className="text-green-300 font-medium">Sécurité Biométrique Avancée</p>
                    <p className="text-green-200/80 text-sm mt-1">
                      Reconnaissance faciale 3D avec détection de vivacité et chiffrement local
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Interface PIN ultra-moderne */}
          {loginMode === 'pin' && (
            <form onSubmit={authenticateWithPIN} className="space-y-6 relative z-10">
              <div className="space-y-5">
                <div>
                  <label className="block text-white font-semibold mb-3 text-sm">
                    Identifiant Professionnel
                  </label>
                  <div className="relative">
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      <User className="w-5 h-5 text-white/50" />
                    </div>
                    <input
                      type="text"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 text-white placeholder-white/50 font-medium"
                      placeholder="Votre identifiant professionnel"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-white font-semibold mb-3 text-sm">
                    Code PIN Sécurisé
                  </label>
                  <div className="relative">
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                      <Lock className="w-5 h-5 text-white/50" />
                    </div>
                    <input
                      type={showPin ? 'text' : 'password'}
                      value={pinCode}
                      onChange={(e) => setPinCode(e.target.value)}
                      className="w-full pl-12 pr-14 py-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 text-white placeholder-white/50 font-medium"
                      placeholder="Votre code PIN sécurisé"
                      maxLength={6}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPin(!showPin)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/80 transition-colors p-1"
                    >
                      {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>
              </div>

              {/* Options avancées */}
              <div className="flex items-center justify-between text-sm">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input type="checkbox" className="w-4 h-4 text-blue-500 bg-white/10 border-white/20 rounded focus:ring-blue-400" />
                  <span className="text-white/80">Mémoriser la session</span>
                </label>
                <button type="button" className="text-blue-300 hover:text-blue-200 font-medium transition-colors">
                  PIN oublié ?
                </button>
              </div>

              {/* Bouton de connexion ultra-premium */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 px-6 rounded-2xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-6 h-6 animate-spin mr-3" />
                    Authentification...
                  </>
                ) : (
                  <>
                    <Lock className="w-6 h-6 mr-3" />
                    Accéder à la Plateforme
                  </>
                )}
              </button>

              {/* Méthodes alternatives premium */}
              <div className="text-center">
                <p className="text-white/60 text-sm mb-4">Ou connectez-vous avec</p>
                <div className="flex justify-center space-x-4">
                  <button
                    type="button"
                    onClick={() => setLoginMode('face')}
                    className="flex items-center space-x-2 px-6 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl hover:bg-white/20 transition-all duration-300"
                  >
                    <Camera className="w-5 h-5 text-white/70" />
                    <span className="text-white/80 text-sm font-medium">Biométrie</span>
                  </button>
                  <button
                    type="button"
                    className="flex items-center space-x-2 px-6 py-3 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl hover:bg-white/20 transition-all duration-300"
                  >
                    <Fingerprint className="w-5 h-5 text-white/70" />
                    <span className="text-white/80 text-sm font-medium">Empreinte</span>
                  </button>
                </div>
              </div>
            </form>
          )}

          {/* Informations de test ultra-modernes */}
          <div className="mt-8 p-4 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 backdrop-blur-xl rounded-2xl border border-blue-400/20 relative z-10">
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="text-blue-300 font-semibold">Compte de Démonstration</span>
              </div>
              <div className="space-y-2 text-sm text-blue-200">
                <div className="flex items-center justify-center space-x-3">
                  <span className="font-medium">Identifiant:</span>
                  <code className="bg-blue-400/20 px-3 py-1 rounded-lg font-mono text-blue-100">directeur.credit</code>
                </div>
                <div className="flex items-center justify-center space-x-3">
                  <span className="font-medium">PIN:</span>
                  <code className="bg-blue-400/20 px-3 py-1 rounded-lg font-mono text-blue-100">1234</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages d'état ultra-modernes */}
      {error && (
        <div className="fixed bottom-8 right-8 bg-red-500/90 backdrop-blur-xl text-white p-6 rounded-2xl shadow-2xl flex items-center z-50 border border-red-400/30 max-w-md">
          <div className="w-10 h-10 bg-red-400 rounded-full flex items-center justify-center mr-4">
            <AlertCircle className="w-5 h-5" />
          </div>
          <div>
            <p className="font-semibold">Erreur d'Authentification</p>
            <p className="text-red-100 text-sm mt-1">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="fixed bottom-8 right-8 bg-green-500/90 backdrop-blur-xl text-white p-6 rounded-2xl shadow-2xl flex items-center z-50 border border-green-400/30 max-w-md">
          <div className="w-10 h-10 bg-green-400 rounded-full flex items-center justify-center mr-4">
            <CheckCircle className="w-5 h-5" />
          </div>
          <div>
            <p className="font-semibold">Authentification Réussie</p>
            <p className="text-green-100 text-sm mt-1">{success}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UltraModernLogin;
