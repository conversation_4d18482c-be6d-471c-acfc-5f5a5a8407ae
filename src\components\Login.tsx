import React, { useState, useRef, useEffect } from 'react';
import { Camera, Lock, Eye, EyeOff, Loader2, AlertCircle, CheckCircle, User, Shield, Fingerprint } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const Login: React.FC = () => {
  const { login } = useAuth();
  const [loginMode, setLoginMode] = useState<'face' | 'pin'>('pin');
  const [username, setUsername] = useState('');
  const [pinCode, setPinCode] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [faceDetectionStatus, setFaceDetectionStatus] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Nettoyage automatique des messages
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  // Nettoyage de la caméra
  useEffect(() => {
    return () => {
      deactivateCamera();
    };
  }, []);

  const activateCamera = async () => {
    try {
      setIsLoading(true);
      setFaceDetectionStatus('Activation de la caméra...');
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: 640, 
          height: 480,
          facingMode: 'user'
        } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraActive(true);
        setFaceDetectionStatus('Caméra activée - Positionnez votre visage');
      }
    } catch (error) {
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
      setFaceDetectionStatus(null);
    } finally {
      setIsLoading(false);
    }
  };

  const deactivateCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setCameraActive(false);
    setFaceDetectionStatus(null);
  };

  const authenticateWithFace = async () => {
    setIsLoading(true);
    setFaceDetectionStatus('Analyse du visage en cours...');
    
    try {
      // Simulation de l'authentification faciale
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setSuccess('Authentification faciale réussie !');
      setFaceDetectionStatus('Visage reconnu avec succès');

      setTimeout(async () => {
        await login('face-id-user', 'face-auth');
      }, 1500);
      
    } catch (error) {
      setError('Échec de l\'authentification faciale');
      setFaceDetectionStatus('Visage non reconnu');
    } finally {
      setIsLoading(false);
    }
  };

  const authenticateWithPIN = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Simulation de l'authentification PIN
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      if (username === 'directeur.credit' && pinCode === '1234') {
        setSuccess('Connexion réussie !');
        setTimeout(async () => {
          await login(username, pinCode);
        }, 1000);
      } else {
        setError('Nom d\'utilisateur ou code PIN incorrect');
      }
    } catch (error) {
      setError('Erreur de connexion');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
      {/* Photo réelle de banque moderne en arrière-plan */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`
        }}
      />

      {/* Overlay avec effet de flou ultra-moderne */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/60 via-blue-900/70 to-indigo-900/80 backdrop-blur-sm"></div>

      {/* Effet de particules lumineuses */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400/60 rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-white/40 rounded-full animate-ping"></div>
        <div className="absolute bottom-1/4 left-1/3 w-3 h-3 bg-blue-300/30 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-indigo-400/50 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 left-1/2 w-2 h-2 bg-white/30 rounded-full animate-ping"></div>
        <div className="absolute top-1/6 right-1/6 w-1 h-1 bg-blue-200/60 rounded-full animate-bounce"></div>
      </div>

      {/* Grille de points lumineux */}
      <div className="absolute inset-0 opacity-20">
        <div className="grid grid-cols-12 gap-8 h-full w-full p-8">
          {Array.from({ length: 144 }).map((_, i) => (
            <div
              key={i}
              className="w-1 h-1 bg-white/20 rounded-full animate-pulse"
              style={{ animationDelay: `${i * 0.1}s` }}
            />
          ))}
        </div>
      </div>
      
      {/* Particules flottantes élégantes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/5 w-1 h-1 bg-white/40 rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-0.5 h-0.5 bg-blue-200/60 rounded-full animate-ping"></div>
        <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-white/20 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-blue-100/50 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 left-1/2 w-1.5 h-1.5 bg-white/30 rounded-full animate-ping"></div>
        <div className="absolute top-1/6 right-1/6 w-0.5 h-0.5 bg-blue-300/70 rounded-full animate-bounce"></div>
      </div>

      {/* Formulaire de connexion centré ultra-moderne */}
      <div className="relative z-10 w-full max-w-lg mx-auto px-6">

        {/* Logo flottant au-dessus */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl mb-6">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-2xl flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                <path d="M3 6h18v12H3V6z" stroke="currentColor" strokeWidth="2" fill="none"/>
                <path d="M6 9h12M6 12h9M6 15h6" stroke="currentColor" strokeWidth="1.5"/>
                <circle cx="18" cy="12" r="1.5" fill="currentColor"/>
              </svg>
            </div>
          </div>
          <h1 className="text-4xl font-bold text-white mb-2 tracking-tight">TuniBankX</h1>
          <p className="text-white/80 text-lg font-medium">Plateforme Bancaire Révolutionnaire</p>
        </div>
        {/* Carte de connexion ultra-moderne */}
        <div className="bg-white/5 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/10 p-8 relative overflow-hidden">
          {/* Effet de brillance premium */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-cyan-600/20 rounded-full blur-3xl"></div>

          {/* En-tête du formulaire premium */}
          <div className="text-center mb-8 relative z-10">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-xl mb-4">
              <Lock className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">Accès Sécurisé</h3>
            <p className="text-white/70">Plateforme Bancaire Professionnelle</p>
          </div>

          {/* Indicateur de sécurité premium */}
          <div className="flex items-center justify-center mb-8 relative z-10">
            <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-xl px-6 py-3 rounded-2xl border border-white/20">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-white/90 text-sm font-medium">Chiffrement AES-256</span>
            </div>
          </div>

            {/* Sélecteur de mode de connexion moderne */}
            <div className="flex bg-gray-100 rounded-2xl p-1 mb-8">
              <button
                onClick={() => {
                  setLoginMode('face');
                  deactivateCamera();
                  setError(null);
                  setSuccess(null);
                }}
                className={`flex-1 py-3 px-6 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                  loginMode === 'face'
                    ? 'bg-white text-blue-600 shadow-md transform scale-105'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Camera className="w-5 h-5" />
                <span>Face ID</span>
                {loginMode === 'face' && (
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                )}
              </button>
              <button
                onClick={() => {
                  setLoginMode('pin');
                  deactivateCamera();
                  setError(null);
                  setSuccess(null);
                }}
                className={`flex-1 py-3 px-6 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center justify-center space-x-2 ${
                  loginMode === 'pin'
                    ? 'bg-white text-blue-600 shadow-md transform scale-105'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Lock className="w-5 h-5" />
                <span>Code PIN</span>
                {loginMode === 'pin' && (
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                )}
              </button>
            </div>

            {/* Interface Face ID moderne */}
            {loginMode === 'face' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Authentification Face ID</h3>
                  <p className="text-gray-600 text-sm">Positionnez votre visage dans le cadre pour vous connecter en toute sécurité</p>
                </div>

                {/* Zone caméra professionnelle */}
                <div className="relative">
                  <div className="w-80 h-60 mx-auto bg-gray-100 rounded-2xl border-2 border-gray-200 flex items-center justify-center overflow-hidden shadow-inner">
                    {cameraActive ? (
                      <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        muted
                        className="w-full h-full object-cover rounded-xl"
                      />
                    ) : (
                      <div className="text-center">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Camera className="w-8 h-8 text-blue-600" />
                        </div>
                        <p className="text-gray-500 text-sm font-medium">Caméra désactivée</p>
                        <p className="text-gray-400 text-xs mt-1">Cliquez pour activer</p>
                      </div>
                    )}
                  </div>

                  {/* Overlay de détection moderne */}
                  {cameraActive && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="relative">
                        <div className="w-56 h-44 border-2 border-blue-500 rounded-2xl animate-pulse shadow-lg"></div>
                        {/* Coins de cadrage */}
                        <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
                        <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
                        <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
                        <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Statut de détection avec design moderne */}
                {faceDetectionStatus && (
                  <div className="text-center bg-blue-50 p-3 rounded-xl border border-blue-200">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <p className="text-blue-700 text-sm font-medium">{faceDetectionStatus}</p>
                    </div>
                  </div>
                )}

                {/* Boutons Face ID modernes */}
                <div className="space-y-3">
                  {!cameraActive ? (
                    <button
                      onClick={activateCamera}
                      disabled={isLoading}
                      className="w-full bg-blue-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-blue-700 transition-all duration-300 disabled:opacity-50 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      {isLoading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <>
                          <Camera className="w-5 h-5 mr-2" />
                          Activer la caméra
                        </>
                      )}
                    </button>
                  ) : (
                    <div className="space-y-3">
                      <button
                        onClick={authenticateWithFace}
                        disabled={isLoading}
                        className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                      >
                        {isLoading ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : (
                          <>
                            <Eye className="w-5 h-5 mr-2" />
                            Scanner le visage
                          </>
                        )}
                      </button>
                      <button
                        onClick={deactivateCamera}
                        className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                      >
                        Arrêter la caméra
                      </button>
                    </div>
                  )}
                </div>

                {/* Informations de sécurité */}
                <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                  <div className="flex items-start space-x-3">
                    <Shield className="w-5 h-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="text-green-800 text-sm font-medium">Sécurité avancée</p>
                      <p className="text-green-700 text-xs mt-1">
                        Vos données biométriques sont chiffrées et ne quittent jamais votre appareil
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Interface PIN moderne */}
            {loginMode === 'pin' && (
              <form onSubmit={authenticateWithPIN} className="space-y-6">
                <div className="space-y-5">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Nom d'utilisateur
                    </label>
                    <div className="relative">
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <User className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 bg-gray-50 focus:bg-white"
                        placeholder="Entrez votre nom d'utilisateur"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Code PIN sécurisé
                    </label>
                    <div className="relative">
                      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <Lock className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        type={showPin ? 'text' : 'password'}
                        value={pinCode}
                        onChange={(e) => setPinCode(e.target.value)}
                        className="w-full pl-12 pr-14 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-gray-900 placeholder-gray-400 bg-gray-50 focus:bg-white"
                        placeholder="Entrez votre code PIN"
                        maxLength={6}
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPin(!showPin)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1"
                      >
                        {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Options supplémentaires */}
                <div className="flex items-center justify-between text-sm">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input type="checkbox" className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                    <span className="text-gray-600">Se souvenir de moi</span>
                  </label>
                  <button type="button" className="text-blue-600 hover:text-blue-800 font-medium">
                    PIN oublié ?
                  </button>
                </div>

                {/* Bouton de connexion premium */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin mr-2" />
                      Connexion en cours...
                    </>
                  ) : (
                    <>
                      <Lock className="w-5 h-5 mr-2" />
                      Se connecter
                    </>
                  )}
                </button>

                {/* Méthodes alternatives */}
                <div className="text-center">
                  <p className="text-gray-500 text-sm mb-4">Ou connectez-vous avec</p>
                  <div className="flex justify-center space-x-4">
                    <button
                      type="button"
                      onClick={() => setLoginMode('face')}
                      className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Camera className="w-4 h-4 text-gray-600" />
                      <span className="text-gray-600 text-sm">Face ID</span>
                    </button>
                    <button
                      type="button"
                      className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Fingerprint className="w-4 h-4 text-gray-600" />
                      <span className="text-gray-600 text-sm">Empreinte</span>
                    </button>
                  </div>
                </div>
              </form>
            )}

            {/* Informations de test modernes */}
            <div className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center mr-2">
                    <User className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-blue-800 font-semibold text-sm">Compte de démonstration</span>
                </div>
                <div className="space-y-1 text-xs text-blue-700">
                  <div className="flex items-center justify-center space-x-2">
                    <span className="font-medium">Utilisateur:</span>
                    <code className="bg-blue-100 px-2 py-1 rounded font-mono">directeur.credit</code>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <span className="font-medium">PIN:</span>
                    <code className="bg-blue-100 px-2 py-1 rounded font-mono">1234</code>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer professionnel */}
      <div className="absolute bottom-0 left-0 right-0 z-10">
        <div className="bg-white/10 backdrop-blur-sm border-t border-white/20 py-4">
          <div className="max-w-6xl mx-auto px-8">
            <div className="flex items-center justify-between text-white/80 text-sm">
              <div className="flex items-center space-x-6">
                <span>© 2024 TuniBankX</span>
                <span>•</span>
                <span>Banque Centrale de Tunisie - Agrément n° 2024-001</span>
              </div>
              <div className="flex items-center space-x-6">
                <button className="hover:text-white transition-colors">Aide</button>
                <span>•</span>
                <button className="hover:text-white transition-colors">Sécurité</button>
                <span>•</span>
                <button className="hover:text-white transition-colors">Contact</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages d'état modernes */}
      {error && (
        <div className="fixed bottom-6 right-6 bg-red-500 text-white p-4 rounded-xl shadow-2xl flex items-center z-50 border border-red-400">
          <div className="w-8 h-8 bg-red-400 rounded-full flex items-center justify-center mr-3">
            <AlertCircle className="w-4 h-4" />
          </div>
          <div>
            <p className="font-medium">Erreur de connexion</p>
            <p className="text-red-100 text-sm">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="fixed bottom-6 right-6 bg-green-500 text-white p-4 rounded-xl shadow-2xl flex items-center z-50 border border-green-400">
          <div className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center mr-3">
            <CheckCircle className="w-4 h-4" />
          </div>
          <div>
            <p className="font-medium">Connexion réussie</p>
            <p className="text-green-100 text-sm">{success}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
