import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Login from './components/Login';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import BankingDashboard from './components/BankingDashboard';
import AdvancedClientManagement from './components/AdvancedClientManagement';
import CreditManagement from './components/CreditManagement';
import TechnologyShowcase from './components/TechnologyShowcase';
import AdvancedChatBot from './components/AdvancedChatBot';

// Composant principal de l'application avec authentification
const MainApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { user, logout } = useAuth();

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <BankingDashboard />;
      case 'clients':
        return <AdvancedClientManagement />;
      case 'credits':
        return <CreditManagement />;
      case 'analytics':
        return <TechnologyShowcase />;
      case 'documents':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Génération de Documents</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Module de génération automatique de contrats en développement...</p>
            </div>
          </div>
        );
      case 'security':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Sécurité & Authentification</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Module de sécurité biométrique en développement...</p>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Paramètres</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Paramètres de la plateforme...</p>
            </div>
          </div>
        );
      case 'help':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Aide & Support</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Centre d'aide et documentation...</p>
            </div>
          </div>
        );
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header currentUser={user ? `${user.prenom} ${user.nom}` : 'Utilisateur'} onLogout={logout} />

      <div className="flex">
        <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />

        <main className="flex-1 min-h-screen">
          {renderContent()}
        </main>
      </div>

      <AdvancedChatBot />
    </div>
  );
};

// Composant racine avec gestion de l'authentification
function App() {
  const [showLogin, setShowLogin] = useState(true);

  return (
    <AuthProvider>
      <AppContent showLogin={showLogin} setShowLogin={setShowLogin} />
    </AuthProvider>
  );
}

// Composant de contenu avec logique d'authentification
const AppContent: React.FC<{ showLogin: boolean; setShowLogin: (show: boolean) => void }> = ({
  showLogin,
  setShowLogin
}) => {
  const { isAuthenticated, loading } = useAuth();

  // Afficher un écran de chargement pendant l'initialisation
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-xl">TB</span>
          </div>
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  // Afficher l'écran de connexion si non authentifié
  if (!isAuthenticated || showLogin) {
    return (
      <Login
        onLoginSuccess={() => setShowLogin(false)}
      />
    );
  }

  // Afficher l'application principale si authentifié
  return <MainApp />;
};

export default App;