// Base de données bancaire complète pour TuniBankX
export interface Client {
  clientID: string;
  nom: string;
  prenom: string;
  dateNaissance: string;
  scoreRisque: number;
  montantCredit: number;
  status: 'actif' | 'en_defaut' | 'suspendu' | 'inactif';
  telephone: string;
  email: string;
  adresse: string;
  profession: string;
  revenuMensuel: number;
  dateCreation: string;
  dernierePaiement?: string;
  faceData?: string;
  // Nouvelles propriétés bancaires
  cin: string;
  situationFamiliale: 'celibataire' | 'marie' | 'divorce' | 'veuf';
  nombreEnfants: number;
  employeur: string;
  ancienneteEmploi: number; // en années
  typeContrat: 'cdi' | 'cdd' | 'freelance' | 'retraite';
  patrimoine: number;
  chargesMensuelles: number;
  autresCredits: number;
  garanties: string[];
  segmentClient: 'particulier' | 'professionnel' | 'entreprise' | 'premium';
  risquePays: 'faible' | 'moyen' | 'eleve';
  blackliste: boolean;
}

export interface Compte {
  compteID: string;
  clientID: string;
  numeroCompte: string;
  typeCompte: 'courant' | 'epargne' | 'terme' | 'credit';
  solde: number;
  devise: string;
  dateOuverture: string;
  statut: 'actif' | 'ferme' | 'bloque';
  plafondRetrait: number;
  plafondVirement: number;
  fraisTenue: number;
  tauxInteret?: number;
}

export interface Transaction {
  transactionID: string;
  compteID: string;
  type: 'debit' | 'credit';
  montant: number;
  devise: string;
  description: string;
  dateTransaction: string;
  beneficiaire?: string;
  reference: string;
  statut: 'execute' | 'en_attente' | 'rejete';
  frais: number;
  soldeApres: number;
  canal: 'agence' | 'atm' | 'internet' | 'mobile' | 'carte';
}

export interface Credit {
  creditID: string;
  clientID: string;
  typeCredit: 'personnel' | 'immobilier' | 'auto' | 'professionnel' | 'revolving';
  montantDemande: number;
  montantAccorde: number;
  tauxInteret: number;
  duree: number; // en mois
  mensualite: number;
  dateDebut: string;
  dateFin: string;
  soldeRestant: number;
  statut: 'en_cours' | 'solde' | 'en_defaut' | 'restructure';
  garanties: string[];
  objectif: string;
  scoreApprobation: number;
  dateApprobation: string;
  agentTraitant: string;
}

export interface Alerte {
  alerteID: string;
  clientID?: string;
  type: 'risque' | 'fraude' | 'retard' | 'limite' | 'conformite';
  niveau: 'info' | 'warning' | 'critical';
  titre: string;
  description: string;
  dateCreation: string;
  statut: 'nouvelle' | 'en_cours' | 'resolue' | 'ignoree';
  assigneA?: string;
  actions: string[];
}

export interface User {
  id: string;
  username: string;
  nom: string;
  prenom: string;
  role: 'directeur_credit' | 'analyste' | 'agent';
  email: string;
  faceData?: string; // Données biométriques pour l'authentification
  pinCode?: string; // PIN de secours
  lastLogin?: string;
  isActive: boolean;
}

// Données de test réalistes pour les clients
export const mockClients: Client[] = [
  {
    clientID: "CLI001",
    nom: "Ben Ali",
    prenom: "Ahmed",
    dateNaissance: "1985-03-15",
    scoreRisque: 75,
    montantCredit: 25000,
    status: "actif",
    telephone: "+216 98 123 456",
    email: "<EMAIL>",
    adresse: "Avenue Habib Bourguiba, Tunis",
    profession: "Ingénieur Informatique",
    revenuMensuel: 2500,
    dateCreation: "2023-01-15",
    dernierePaiement: "2024-01-10",
    cin: "12345678",
    situationFamiliale: "marie",
    nombreEnfants: 2,
    employeur: "Tunisie Telecom",
    ancienneteEmploi: 8,
    typeContrat: "cdi",
    patrimoine: 150000,
    chargesMensuelles: 1200,
    autresCredits: 0,
    garanties: ["salaire", "assurance_vie"],
    segmentClient: "particulier",
    risquePays: "faible",
    blackliste: false
  },
  {
    clientID: "CLI002",
    nom: "Trabelsi",
    prenom: "Fatma",
    dateNaissance: "1990-07-22",
    scoreRisque: 82,
    montantCredit: 15000,
    status: "actif",
    telephone: "+216 97 234 567",
    email: "<EMAIL>",
    adresse: "Rue de la République, Sfax",
    profession: "Médecin Généraliste",
    revenuMensuel: 3200,
    dateCreation: "2023-03-20",
    dernierePaiement: "2024-01-08",
    cin: "87654321",
    situationFamiliale: "celibataire",
    nombreEnfants: 0,
    employeur: "Clinique Internationale",
    ancienneteEmploi: 5,
    typeContrat: "cdi",
    patrimoine: 80000,
    chargesMensuelles: 800,
    autresCredits: 5000,
    garanties: ["salaire", "bien_immobilier"],
    segmentClient: "premium",
    risquePays: "faible",
    blackliste: false
  },
  {
    clientID: "CLI003",
    nom: "Cherif",
    prenom: "Mohamed",
    dateNaissance: "1978-11-08",
    scoreRisque: 45,
    montantCredit: 35000,
    status: "en_defaut",
    telephone: "+216 96 345 678",
    email: "<EMAIL>",
    adresse: "Boulevard du 7 Novembre, Sousse",
    profession: "Commerçant",
    revenuMensuel: 1800,
    dateCreation: "2022-08-10",
    dernierePaiement: "2023-11-15"
  },
  {
    clientID: "CLI004",
    nom: "Karray",
    prenom: "Leila",
    dateNaissance: "1992-05-30",
    scoreRisque: 88,
    montantCredit: 12000,
    status: "actif",
    telephone: "+216 95 456 789",
    email: "<EMAIL>",
    adresse: "Avenue de la Liberté, Monastir",
    profession: "Professeure",
    revenuMensuel: 2200,
    dateCreation: "2023-06-12",
    dernierePaiement: "2024-01-05"
  },
  {
    clientID: "CLI005",
    nom: "Mansouri",
    prenom: "Karim",
    dateNaissance: "1987-12-03",
    scoreRisque: 65,
    montantCredit: 28000,
    status: "actif",
    telephone: "+216 94 567 890",
    email: "<EMAIL>",
    adresse: "Rue Ibn Khaldoun, Bizerte",
    profession: "Architecte",
    revenuMensuel: 2800,
    dateCreation: "2023-02-28",
    dernierePaiement: "2024-01-12"
  },
  {
    clientID: "CLI006",
    nom: "Bouazizi",
    prenom: "Sarra",
    dateNaissance: "1995-09-18",
    scoreRisque: 92,
    montantCredit: 8000,
    status: "actif",
    telephone: "+216 93 678 901",
    email: "<EMAIL>",
    adresse: "Place de l'Indépendance, Kairouan",
    profession: "Pharmacienne",
    revenuMensuel: 2600,
    dateCreation: "2023-09-05",
    dernierePaiement: "2024-01-09"
  },
  {
    clientID: "CLI007",
    nom: "Jemli",
    prenom: "Youssef",
    dateNaissance: "1983-04-25",
    scoreRisque: 38,
    montantCredit: 42000,
    status: "suspendu",
    telephone: "+216 92 789 012",
    email: "<EMAIL>",
    adresse: "Avenue Farhat Hached, Gabès",
    profession: "Entrepreneur",
    revenuMensuel: 1500,
    dateCreation: "2022-11-18",
    dernierePaiement: "2023-09-20"
  },
  {
    clientID: "CLI008",
    nom: "Hamdi",
    prenom: "Nadia",
    dateNaissance: "1989-01-14",
    scoreRisque: 78,
    montantCredit: 18000,
    status: "actif",
    telephone: "+216 91 890 123",
    email: "<EMAIL>",
    adresse: "Rue de Marseille, La Marsa",
    profession: "Avocate",
    revenuMensuel: 3000,
    dateCreation: "2023-04-22",
    dernierePaiement: "2024-01-11"
  }
];

// Données de test pour les comptes
export const mockComptes: Compte[] = [
  {
    compteID: "CPT001",
    clientID: "CLI001",
    numeroCompte: "TN59 1000 0001 2345 6789",
    typeCompte: "courant",
    solde: 15420.50,
    devise: "TND",
    dateOuverture: "2023-01-15",
    statut: "actif",
    plafondRetrait: 2000,
    plafondVirement: 5000,
    fraisTenue: 5,
    tauxInteret: 0.5
  },
  {
    compteID: "CPT002",
    clientID: "CLI001",
    numeroCompte: "TN59 1000 0001 2345 6790",
    typeCompte: "epargne",
    solde: 45000,
    devise: "TND",
    dateOuverture: "2023-02-01",
    statut: "actif",
    plafondRetrait: 1000,
    plafondVirement: 2000,
    fraisTenue: 0,
    tauxInteret: 3.5
  },
  {
    compteID: "CPT003",
    clientID: "CLI002",
    numeroCompte: "TN59 1000 0002 3456 7891",
    typeCompte: "courant",
    solde: 8750.25,
    devise: "TND",
    dateOuverture: "2023-03-20",
    statut: "actif",
    plafondRetrait: 3000,
    plafondVirement: 10000,
    fraisTenue: 8,
    tauxInteret: 0.5
  }
];

// Données de test pour les transactions
export const mockTransactions: Transaction[] = [
  {
    transactionID: "TXN001",
    compteID: "CPT001",
    type: "credit",
    montant: 2500,
    devise: "TND",
    description: "Virement salaire",
    dateTransaction: "2024-01-15T08:30:00Z",
    beneficiaire: "Ahmed Ben Ali",
    reference: "SAL202401",
    statut: "execute",
    frais: 0,
    soldeApres: 15420.50,
    canal: "internet"
  },
  {
    transactionID: "TXN002",
    compteID: "CPT001",
    type: "debit",
    montant: 450,
    devise: "TND",
    description: "Retrait ATM",
    dateTransaction: "2024-01-14T14:20:00Z",
    reference: "ATM202401001",
    statut: "execute",
    frais: 2,
    soldeApres: 12968.50,
    canal: "atm"
  },
  {
    transactionID: "TXN003",
    compteID: "CPT002",
    type: "credit",
    montant: 1000,
    devise: "TND",
    description: "Virement depuis compte courant",
    dateTransaction: "2024-01-13T10:15:00Z",
    beneficiaire: "Compte épargne",
    reference: "VIR202401001",
    statut: "execute",
    frais: 1,
    soldeApres: 45000,
    canal: "internet"
  }
];

// Données de test pour les crédits
export const mockCredits: Credit[] = [
  {
    creditID: "CRD001",
    clientID: "CLI001",
    typeCredit: "immobilier",
    montantDemande: 120000,
    montantAccorde: 100000,
    tauxInteret: 4.5,
    duree: 240,
    mensualite: 632.50,
    dateDebut: "2023-02-01",
    dateFin: "2043-02-01",
    soldeRestant: 95000,
    statut: "en_cours",
    garanties: ["hypotheque", "assurance_deces"],
    objectif: "Achat résidence principale",
    scoreApprobation: 85,
    dateApprobation: "2023-01-25",
    agentTraitant: "Mohamed Sassi"
  },
  {
    creditID: "CRD002",
    clientID: "CLI002",
    typeCredit: "auto",
    montantDemande: 25000,
    montantAccorde: 20000,
    tauxInteret: 6.2,
    duree: 60,
    mensualite: 387.20,
    dateDebut: "2023-04-01",
    dateFin: "2028-04-01",
    soldeRestant: 15000,
    statut: "en_cours",
    garanties: ["vehicule", "assurance_auto"],
    objectif: "Achat véhicule neuf",
    scoreApprobation: 78,
    dateApprobation: "2023-03-28",
    agentTraitant: "Leila Karray"
  }
];

// Données de test pour les alertes
export const mockAlertes: Alerte[] = [
  {
    alerteID: "ALT001",
    clientID: "CLI003",
    type: "retard",
    niveau: "warning",
    titre: "Retard de paiement détecté",
    description: "Le client Mohamed Cherif a un retard de 15 jours sur sa mensualité de crédit",
    dateCreation: "2024-01-15T09:00:00Z",
    statut: "nouvelle",
    assigneA: "Mohamed Sassi",
    actions: ["Appel client", "Envoi mise en demeure", "Négociation échéancier"]
  },
  {
    alerteID: "ALT002",
    type: "fraude",
    niveau: "critical",
    titre: "Transaction suspecte détectée",
    description: "Transaction de 5000 TND à l'étranger sur compte CLI001",
    dateCreation: "2024-01-14T22:30:00Z",
    statut: "en_cours",
    assigneA: "Service Sécurité",
    actions: ["Blocage temporaire", "Vérification client", "Analyse transaction"]
  },
  {
    alerteID: "ALT003",
    clientID: "CLI001",
    type: "limite",
    niveau: "info",
    titre: "Approche limite de découvert",
    description: "Le solde du compte approche de la limite de découvert autorisée",
    dateCreation: "2024-01-13T16:45:00Z",
    statut: "resolue",
    actions: ["Notification client", "Proposition crédit relais"]
  }
];

// Utilisateurs de test pour l'authentification
export const mockUsers: User[] = [
  {
    id: "USR001",
    username: "directeur.credit",
    nom: "Directeur",
    prenom: "Crédit",
    role: "directeur_credit",
    email: "<EMAIL>",
    pinCode: "1234",
    isActive: true,
    lastLogin: "2024-01-15T08:30:00Z"
  },
  {
    id: "USR002",
    username: "analyste.risque",
    nom: "Analyste",
    prenom: "Risque",
    role: "analyste",
    email: "<EMAIL>",
    pinCode: "5678",
    isActive: true
  }
];

// Fonctions utilitaires pour la base de données
export class DatabaseService {
  private static instance: DatabaseService;
  private clients: Client[] = [...mockClients];
  private users: User[] = [...mockUsers];

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Gestion des clients
  getAllClients(): Client[] {
    return this.clients;
  }

  getClientById(id: string): Client | undefined {
    return this.clients.find(client => client.clientID === id);
  }

  addClient(client: Client): void {
    this.clients.push(client);
  }

  updateClient(id: string, updates: Partial<Client>): boolean {
    const index = this.clients.findIndex(client => client.clientID === id);
    if (index !== -1) {
      this.clients[index] = { ...this.clients[index], ...updates };
      return true;
    }
    return false;
  }

  deleteClient(id: string): boolean {
    const index = this.clients.findIndex(client => client.clientID === id);
    if (index !== -1) {
      this.clients.splice(index, 1);
      return true;
    }
    return false;
  }

  // Gestion des utilisateurs
  getAllUsers(): User[] {
    return this.users;
  }

  getUserById(id: string): User | undefined {
    return this.users.find(user => user.id === id);
  }

  getUserByUsername(username: string): User | undefined {
    return this.users.find(user => user.username === username);
  }

  updateUser(id: string, updates: Partial<User>): boolean {
    const index = this.users.findIndex(user => user.id === id);
    if (index !== -1) {
      this.users[index] = { ...this.users[index], ...updates };
      return true;
    }
    return false;
  }

  // Statistiques pour le dashboard
  getStatistics() {
    const totalClients = this.clients.length;
    const activeClients = this.clients.filter(c => c.status === 'actif').length;
    const defaultClients = this.clients.filter(c => c.status === 'en_defaut').length;
    const totalCredit = this.clients.reduce((sum, c) => sum + c.montantCredit, 0);
    const averageScore = this.clients.reduce((sum, c) => sum + c.scoreRisque, 0) / totalClients;

    return {
      totalClients,
      activeClients,
      defaultClients,
      suspendedClients: this.clients.filter(c => c.status === 'suspendu').length,
      totalCredit,
      averageScore: Math.round(averageScore),
      defaultRate: ((defaultClients / totalClients) * 100).toFixed(1)
    };
  }
}
