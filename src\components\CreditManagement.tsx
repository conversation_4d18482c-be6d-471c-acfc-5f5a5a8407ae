import React, { useState, useEffect } from 'react';
import { 
  Plus, Search, Filter, Eye, Edit, Check, X, Clock, AlertTriangle,
  TrendingUp, TrendingDown, DollarSign, Calendar, User, FileText,
  CreditCard, Target, Activity, Shield, Zap, Download, Upload
} from 'lucide-react';
import { DatabaseService, Credit, mockCredits, Client } from '../data/database';

interface CreditWithClient extends Credit {
  clientNom: string;
  clientPrenom: string;
  clientEmail: string;
  clientScore: number;
  progression: number;
  prochainePaiement: string;
  retardJours?: number;
}

interface CreditStats {
  totalCredits: number;
  montantTotal: number;
  tauxDefaut: number;
  creditsPendants: number;
  creditsApprouves: number;
  creditsRejetes: number;
}

const CreditManagement: React.FC = () => {
  const [credits, setCredits] = useState<CreditWithClient[]>([]);
  const [filteredCredits, setFilteredCredits] = useState<CreditWithClient[]>([]);
  const [selectedCredit, setSelectedCredit] = useState<CreditWithClient | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [stats, setStats] = useState<CreditStats>({
    totalCredits: 0,
    montantTotal: 0,
    tauxDefaut: 0,
    creditsPendants: 0,
    creditsApprouves: 0,
    creditsRejetes: 0
  });
  const [loading, setLoading] = useState(true);
  const [showNewCreditModal, setShowNewCreditModal] = useState(false);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadCredits();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [credits, searchTerm, filterType, filterStatus]);

  const loadCredits = async () => {
    try {
      const creditsData = mockCredits;
      const clientsData = db.getAllClients();
      
      // Enrichir les crédits avec les informations clients
      const enrichedCredits: CreditWithClient[] = creditsData.map(credit => {
        const client = clientsData.find(c => c.clientID === credit.clientID);
        const progression = ((credit.montantAccorde - credit.soldeRestant) / credit.montantAccorde) * 100;
        
        // Calculer la prochaine échéance
        const dateDebut = new Date(credit.dateDebut);
        const prochainePaiement = new Date(dateDebut);
        prochainePaiement.setMonth(prochainePaiement.getMonth() + 1);
        
        // Simuler des retards pour certains crédits
        const retardJours = credit.statut === 'en_defaut' ? Math.floor(Math.random() * 30) + 1 : undefined;

        return {
          ...credit,
          clientNom: client?.nom || 'Inconnu',
          clientPrenom: client?.prenom || 'Inconnu',
          clientEmail: client?.email || '',
          clientScore: client?.scoreRisque || 0,
          progression,
          prochainePaiement: prochainePaiement.toISOString(),
          retardJours
        };
      });

      // Calculer les statistiques
      const statsData: CreditStats = {
        totalCredits: enrichedCredits.length,
        montantTotal: enrichedCredits.reduce((sum, c) => sum + c.montantAccorde, 0),
        tauxDefaut: (enrichedCredits.filter(c => c.statut === 'en_defaut').length / enrichedCredits.length) * 100,
        creditsPendants: enrichedCredits.filter(c => c.statut === 'en_cours').length,
        creditsApprouves: enrichedCredits.filter(c => c.statut === 'en_cours' || c.statut === 'solde').length,
        creditsRejetes: 0 // Simulé
      };

      setCredits(enrichedCredits);
      setStats(statsData);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement des crédits:', error);
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = credits;

    // Filtre par recherche
    if (searchTerm) {
      filtered = filtered.filter(credit =>
        `${credit.clientPrenom} ${credit.clientNom}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        credit.creditID.toLowerCase().includes(searchTerm.toLowerCase()) ||
        credit.objectif.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par type
    if (filterType !== 'all') {
      filtered = filtered.filter(credit => credit.typeCredit === filterType);
    }

    // Filtre par statut
    if (filterStatus !== 'all') {
      filtered = filtered.filter(credit => credit.statut === filterStatus);
    }

    setFilteredCredits(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'en_cours': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'solde': return 'text-green-600 bg-green-50 border-green-200';
      case 'en_defaut': return 'text-red-600 bg-red-50 border-red-200';
      case 'restructure': return 'text-orange-600 bg-orange-50 border-orange-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'immobilier': return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'auto': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'personnel': return 'text-green-600 bg-green-50 border-green-200';
      case 'professionnel': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'revolving': return 'text-pink-600 bg-pink-50 border-pink-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getProgressColor = (progression: number) => {
    if (progression >= 80) return 'bg-green-500';
    if (progression >= 50) return 'bg-blue-500';
    if (progression >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Crédits</h2>
          <p className="text-gray-600">
            {filteredCredits.length} crédits sur {credits.length} total
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={() => setShowNewCreditModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nouveau Crédit
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <Upload className="w-4 h-4 mr-2" />
            Importer
          </button>
          <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Rapport
          </button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Crédits</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCredits}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 text-sm font-medium">+8.2%</span>
            <span className="text-gray-500 text-sm ml-2">vs mois dernier</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Montant Total</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.montantTotal)}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 text-sm font-medium">+12.5%</span>
            <span className="text-gray-500 text-sm ml-2">vs mois dernier</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Taux de Défaut</p>
              <p className="text-2xl font-bold text-gray-900">{stats.tauxDefaut.toFixed(1)}%</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingDown className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 text-sm font-medium">-0.8%</span>
            <span className="text-gray-500 text-sm ml-2">vs mois dernier</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">En Cours</p>
              <p className="text-2xl font-bold text-gray-900">{stats.creditsPendants}</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <Activity className="w-4 h-4 text-blue-500 mr-1" />
            <span className="text-blue-600 text-sm font-medium">Actif</span>
            <span className="text-gray-500 text-sm ml-2">Suivi temps réel</span>
          </div>
        </div>
      </div>

      {/* Filtres */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher un crédit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tous les types</option>
            <option value="immobilier">Immobilier</option>
            <option value="auto">Automobile</option>
            <option value="personnel">Personnel</option>
            <option value="professionnel">Professionnel</option>
            <option value="revolving">Revolving</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="en_cours">En Cours</option>
            <option value="solde">Soldé</option>
            <option value="en_defaut">En Défaut</option>
            <option value="restructure">Restructuré</option>
          </select>

          <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center">
            <Filter className="w-4 h-4 mr-2" />
            Filtres Avancés
          </button>
        </div>
      </div>

      {/* Liste des crédits */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progression</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Échéance</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCredits.map((credit) => (
                <tr key={credit.creditID} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white font-medium text-sm">
                          {credit.clientPrenom[0]}{credit.clientNom[0]}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {credit.clientPrenom} {credit.clientNom}
                        </div>
                        <div className="text-sm text-gray-500">
                          Score: {credit.clientScore}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getTypeColor(credit.typeCredit)}`}>
                      {credit.typeCredit}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(credit.montantAccorde)}
                      </div>
                      <div className="text-sm text-gray-500">
                        Reste: {formatCurrency(credit.soldeRestant)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getProgressColor(credit.progression)}`}
                        style={{ width: `${credit.progression}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {credit.progression.toFixed(1)}% remboursé
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(credit.statut)}`}>
                      {credit.statut === 'en_cours' ? 'En Cours' :
                       credit.statut === 'solde' ? 'Soldé' :
                       credit.statut === 'en_defaut' ? 'En Défaut' : 'Restructuré'}
                    </span>
                    {credit.retardJours && (
                      <div className="text-xs text-red-600 mt-1">
                        Retard: {credit.retardJours}j
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(credit.prochainePaiement).toLocaleDateString('fr-FR')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => setSelectedCredit(credit)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Voir détails"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900 p-1 rounded" title="Modifier">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-purple-600 hover:text-purple-900 p-1 rounded" title="Générer contrat">
                        <FileText className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de détails du crédit */}
      {selectedCredit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">
                  Détails du Crédit - {selectedCredit.creditID}
                </h3>
                <button 
                  onClick={() => setSelectedCredit(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">Informations du Crédit</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Type:</span>
                      <span className="text-sm font-medium text-gray-900">{selectedCredit.typeCredit}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Montant accordé:</span>
                      <span className="text-sm font-medium text-gray-900">{formatCurrency(selectedCredit.montantAccorde)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Taux d'intérêt:</span>
                      <span className="text-sm font-medium text-gray-900">{selectedCredit.tauxInteret}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Durée:</span>
                      <span className="text-sm font-medium text-gray-900">{selectedCredit.duree} mois</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Mensualité:</span>
                      <span className="text-sm font-medium text-gray-900">{formatCurrency(selectedCredit.mensualite)}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">Informations Client</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Nom:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedCredit.clientPrenom} {selectedCredit.clientNom}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Score de risque:</span>
                      <span className="text-sm font-medium text-gray-900">{selectedCredit.clientScore}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Agent traitant:</span>
                      <span className="text-sm font-medium text-gray-900">{selectedCredit.agentTraitant}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Date d'approbation:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {new Date(selectedCredit.dateApprobation).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Progression du Remboursement</h4>
                <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
                  <div 
                    className={`h-4 rounded-full ${getProgressColor(selectedCredit.progression)}`}
                    style={{ width: `${selectedCredit.progression}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>{selectedCredit.progression.toFixed(1)}% remboursé</span>
                  <span>Reste: {formatCurrency(selectedCredit.soldeRestant)}</span>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  Générer Échéancier
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Modifier Crédit
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreditManagement;
