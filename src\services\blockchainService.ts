// Service Blockchain pour TuniBankX - Sécurité et Traçabilité Avancées
// Système révolutionnaire de blockchain pour les transactions bancaires

import { createHash } from 'crypto';

export interface BlockchainTransaction {
  id: string;
  fromAccount: string;
  toAccount: string;
  amount: number;
  currency: string;
  type: 'transfer' | 'credit' | 'debit' | 'fee' | 'interest';
  description: string;
  timestamp: number;
  metadata: {
    agentId?: string;
    branchId?: string;
    deviceId?: string;
    ipAddress?: string;
    geoLocation?: string;
    riskScore?: number;
  };
}

export interface Block {
  index: number;
  timestamp: number;
  transactions: BlockchainTransaction[];
  previousHash: string;
  hash: string;
  nonce: number;
  merkleRoot: string;
  validator: string;
  signature: string;
}

export interface BlockchainStats {
  totalBlocks: number;
  totalTransactions: number;
  totalVolume: number;
  averageBlockTime: number;
  networkHashRate: number;
  lastBlockTime: number;
  pendingTransactions: number;
}

export interface AuditTrail {
  transactionId: string;
  blockIndex: number;
  blockHash: string;
  confirmations: number;
  isValid: boolean;
  timestamp: number;
  auditPath: string[];
}

export class TuniBankXBlockchain {
  private static instance: TuniBankXBlockchain;
  private chain: Block[] = [];
  private pendingTransactions: BlockchainTransaction[] = [];
  private miningReward = 0; // Pas de mining reward pour une blockchain privée
  private difficulty = 4; // Difficulté de minage
  private validators: string[] = ['TuniBankX-Node-1', 'TuniBankX-Node-2', 'TuniBankX-Node-3'];
  private currentValidator = 0;

  constructor() {
    this.createGenesisBlock();
  }

  static getInstance(): TuniBankXBlockchain {
    if (!TuniBankXBlockchain.instance) {
      TuniBankXBlockchain.instance = new TuniBankXBlockchain();
    }
    return TuniBankXBlockchain.instance;
  }

  // Création du bloc genesis
  private createGenesisBlock(): void {
    const genesisBlock: Block = {
      index: 0,
      timestamp: Date.now(),
      transactions: [],
      previousHash: '0',
      hash: '',
      nonce: 0,
      merkleRoot: '',
      validator: 'TuniBankX-Genesis',
      signature: ''
    };

    genesisBlock.merkleRoot = this.calculateMerkleRoot(genesisBlock.transactions);
    genesisBlock.hash = this.calculateHash(genesisBlock);
    genesisBlock.signature = this.signBlock(genesisBlock);

    this.chain.push(genesisBlock);
  }

  // Ajout d'une transaction à la blockchain
  public addTransaction(transaction: BlockchainTransaction): string {
    // Validation de la transaction
    if (!this.validateTransaction(transaction)) {
      throw new Error('Transaction invalide');
    }

    // Ajout de métadonnées de sécurité
    transaction.metadata = {
      ...transaction.metadata,
      timestamp: Date.now(),
      riskScore: this.calculateTransactionRisk(transaction)
    };

    this.pendingTransactions.push(transaction);

    // Auto-minage si assez de transactions
    if (this.pendingTransactions.length >= 5) {
      this.mineBlock();
    }

    return transaction.id;
  }

  // Minage d'un nouveau bloc
  public mineBlock(): Block {
    const newBlock: Block = {
      index: this.chain.length,
      timestamp: Date.now(),
      transactions: [...this.pendingTransactions],
      previousHash: this.getLatestBlock().hash,
      hash: '',
      nonce: 0,
      merkleRoot: '',
      validator: this.validators[this.currentValidator],
      signature: ''
    };

    // Calcul du Merkle Root
    newBlock.merkleRoot = this.calculateMerkleRoot(newBlock.transactions);

    // Proof of Work (simplifié pour la démo)
    newBlock.hash = this.mineBlockHash(newBlock);

    // Signature du bloc
    newBlock.signature = this.signBlock(newBlock);

    // Ajout à la chaîne
    this.chain.push(newBlock);

    // Rotation du validateur
    this.currentValidator = (this.currentValidator + 1) % this.validators.length;

    // Nettoyage des transactions en attente
    this.pendingTransactions = [];

    return newBlock;
  }

  // Validation d'une transaction
  private validateTransaction(transaction: BlockchainTransaction): boolean {
    // Vérifications de base
    if (!transaction.id || !transaction.fromAccount || !transaction.toAccount) {
      return false;
    }

    if (transaction.amount <= 0) {
      return false;
    }

    // Vérification des comptes
    if (transaction.fromAccount === transaction.toAccount) {
      return false;
    }

    // Vérification de la devise
    if (!['TND', 'EUR', 'USD'].includes(transaction.currency)) {
      return false;
    }

    // Vérifications de sécurité avancées
    if (this.isTransactionSuspicious(transaction)) {
      return false;
    }

    return true;
  }

  // Détection de transactions suspectes
  private isTransactionSuspicious(transaction: BlockchainTransaction): boolean {
    // Montants suspects
    if (transaction.amount > 100000) {
      return true; // Montant très élevé
    }

    // Fréquence suspecte
    const recentTransactions = this.getRecentTransactions(transaction.fromAccount, 3600000); // 1 heure
    if (recentTransactions.length > 10) {
      return true; // Trop de transactions récentes
    }

    // Patterns suspects
    const totalRecentAmount = recentTransactions.reduce((sum, tx) => sum + tx.amount, 0);
    if (totalRecentAmount > 50000) {
      return true; // Volume suspect
    }

    return false;
  }

  // Calcul du risque d'une transaction
  private calculateTransactionRisk(transaction: BlockchainTransaction): number {
    let riskScore = 0;

    // Facteurs de risque
    if (transaction.amount > 10000) riskScore += 20;
    if (transaction.amount > 50000) riskScore += 40;
    if (transaction.type === 'transfer' && transaction.amount > 5000) riskScore += 15;

    // Historique du compte
    const accountHistory = this.getAccountHistory(transaction.fromAccount);
    if (accountHistory.length < 5) riskScore += 25; // Compte nouveau

    // Géolocalisation
    if (transaction.metadata.geoLocation && this.isHighRiskLocation(transaction.metadata.geoLocation)) {
      riskScore += 30;
    }

    // Heure de transaction
    const hour = new Date(transaction.timestamp).getHours();
    if (hour < 6 || hour > 22) riskScore += 10; // Heures inhabituelles

    return Math.min(100, riskScore);
  }

  // Vérification de localisation à risque
  private isHighRiskLocation(location: string): boolean {
    const highRiskCountries = ['XX', 'YY']; // Liste des pays à risque
    return highRiskCountries.some(country => location.includes(country));
  }

  // Calcul du hash d'un bloc avec Proof of Work
  private mineBlockHash(block: Block): string {
    const target = Array(this.difficulty + 1).join('0');
    
    while (block.hash.substring(0, this.difficulty) !== target) {
      block.nonce++;
      block.hash = this.calculateHash(block);
    }

    return block.hash;
  }

  // Calcul du hash d'un bloc
  private calculateHash(block: Block): string {
    const data = block.index + 
                 block.timestamp + 
                 JSON.stringify(block.transactions) + 
                 block.previousHash + 
                 block.nonce + 
                 block.merkleRoot;
    
    return createHash('sha256').update(data).digest('hex');
  }

  // Calcul du Merkle Root
  private calculateMerkleRoot(transactions: BlockchainTransaction[]): string {
    if (transactions.length === 0) {
      return createHash('sha256').update('').digest('hex');
    }

    let hashes = transactions.map(tx => 
      createHash('sha256').update(JSON.stringify(tx)).digest('hex')
    );

    while (hashes.length > 1) {
      const newHashes: string[] = [];
      
      for (let i = 0; i < hashes.length; i += 2) {
        const left = hashes[i];
        const right = hashes[i + 1] || left; // Dupliquer si impair
        const combined = createHash('sha256').update(left + right).digest('hex');
        newHashes.push(combined);
      }
      
      hashes = newHashes;
    }

    return hashes[0];
  }

  // Signature d'un bloc (simulation)
  private signBlock(block: Block): string {
    const data = block.hash + block.validator;
    return createHash('sha256').update(data).digest('hex');
  }

  // Validation de la chaîne complète
  public validateChain(): boolean {
    for (let i = 1; i < this.chain.length; i++) {
      const currentBlock = this.chain[i];
      const previousBlock = this.chain[i - 1];

      // Vérification du hash
      if (currentBlock.hash !== this.calculateHash(currentBlock)) {
        return false;
      }

      // Vérification du lien avec le bloc précédent
      if (currentBlock.previousHash !== previousBlock.hash) {
        return false;
      }

      // Vérification du Merkle Root
      if (currentBlock.merkleRoot !== this.calculateMerkleRoot(currentBlock.transactions)) {
        return false;
      }
    }

    return true;
  }

  // Recherche d'une transaction
  public findTransaction(transactionId: string): AuditTrail | null {
    for (let i = 0; i < this.chain.length; i++) {
      const block = this.chain[i];
      const transaction = block.transactions.find(tx => tx.id === transactionId);
      
      if (transaction) {
        return {
          transactionId,
          blockIndex: block.index,
          blockHash: block.hash,
          confirmations: this.chain.length - block.index,
          isValid: this.validateChain(),
          timestamp: block.timestamp,
          auditPath: this.generateAuditPath(transactionId, block)
        };
      }
    }

    return null;
  }

  // Génération du chemin d'audit
  private generateAuditPath(transactionId: string, block: Block): string[] {
    const path: string[] = [];
    
    path.push(`Transaction ID: ${transactionId}`);
    path.push(`Block Index: ${block.index}`);
    path.push(`Block Hash: ${block.hash}`);
    path.push(`Validator: ${block.validator}`);
    path.push(`Timestamp: ${new Date(block.timestamp).toISOString()}`);
    path.push(`Merkle Root: ${block.merkleRoot}`);
    
    return path;
  }

  // Obtenir les transactions récentes d'un compte
  private getRecentTransactions(accountId: string, timeWindow: number): BlockchainTransaction[] {
    const cutoffTime = Date.now() - timeWindow;
    const transactions: BlockchainTransaction[] = [];

    for (const block of this.chain) {
      if (block.timestamp >= cutoffTime) {
        const accountTransactions = block.transactions.filter(
          tx => tx.fromAccount === accountId || tx.toAccount === accountId
        );
        transactions.push(...accountTransactions);
      }
    }

    return transactions;
  }

  // Obtenir l'historique d'un compte
  private getAccountHistory(accountId: string): BlockchainTransaction[] {
    const transactions: BlockchainTransaction[] = [];

    for (const block of this.chain) {
      const accountTransactions = block.transactions.filter(
        tx => tx.fromAccount === accountId || tx.toAccount === accountId
      );
      transactions.push(...accountTransactions);
    }

    return transactions;
  }

  // Statistiques de la blockchain
  public getBlockchainStats(): BlockchainStats {
    const totalTransactions = this.chain.reduce((sum, block) => sum + block.transactions.length, 0);
    const totalVolume = this.chain.reduce((sum, block) => 
      sum + block.transactions.reduce((blockSum, tx) => blockSum + tx.amount, 0), 0
    );

    let totalBlockTime = 0;
    for (let i = 1; i < this.chain.length; i++) {
      totalBlockTime += this.chain[i].timestamp - this.chain[i - 1].timestamp;
    }
    const averageBlockTime = this.chain.length > 1 ? totalBlockTime / (this.chain.length - 1) : 0;

    return {
      totalBlocks: this.chain.length,
      totalTransactions,
      totalVolume,
      averageBlockTime,
      networkHashRate: this.calculateNetworkHashRate(),
      lastBlockTime: this.getLatestBlock().timestamp,
      pendingTransactions: this.pendingTransactions.length
    };
  }

  // Calcul du taux de hash du réseau (simulation)
  private calculateNetworkHashRate(): number {
    // Simulation basée sur la difficulté et le temps de bloc
    return Math.pow(2, this.difficulty) * 1000; // Hash/sec simulé
  }

  // Obtenir le dernier bloc
  public getLatestBlock(): Block {
    return this.chain[this.chain.length - 1];
  }

  // Obtenir la chaîne complète
  public getChain(): Block[] {
    return [...this.chain];
  }

  // Obtenir les transactions en attente
  public getPendingTransactions(): BlockchainTransaction[] {
    return [...this.pendingTransactions];
  }

  // Export pour audit externe
  public exportForAudit(): any {
    return {
      chain: this.chain,
      stats: this.getBlockchainStats(),
      isValid: this.validateChain(),
      exportTimestamp: Date.now(),
      version: '1.0.0'
    };
  }

  // Vérification d'intégrité en temps réel
  public performIntegrityCheck(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Vérification de la chaîne
    if (!this.validateChain()) {
      issues.push('Chaîne de blocs corrompue');
    }

    // Vérification des signatures
    for (const block of this.chain) {
      if (block.signature !== this.signBlock(block)) {
        issues.push(`Signature invalide pour le bloc ${block.index}`);
      }
    }

    // Vérification des validateurs
    for (const block of this.chain.slice(1)) {
      if (!this.validators.includes(block.validator)) {
        issues.push(`Validateur non autorisé: ${block.validator}`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}
