import React, { useState } from 'react';
import { 
  Bell, Search, Settings, User, LogOut, Globe, Shield, 
  TrendingUp, AlertTriangle, CheckCircle, Clock, 
  ChevronDown, Menu, X, Zap, Star, Award
} from 'lucide-react';

interface EnterpriseHeaderProps {
  user: any;
  onLogout: () => void;
}

const EnterpriseHeader: React.FC<EnterpriseHeaderProps> = ({ user, onLogout }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  const notifications = [
    {
      id: 1,
      type: 'alert',
      title: 'Alerte de Sécurité',
      message: 'Nouvelle connexion détectée depuis Tunis',
      time: '2 min',
      icon: Shield,
      color: 'text-red-500'
    },
    {
      id: 2,
      type: 'success',
      title: 'Transaction Approuvée',
      message: 'Crédit de 150,000 TND approuvé automatiquement',
      time: '5 min',
      icon: CheckCircle,
      color: 'text-green-500'
    },
    {
      id: 3,
      type: 'info',
      title: 'Rapport IA Disponible',
      message: 'Analyse prédictive Q4 2024 générée',
      time: '15 min',
      icon: TrendingUp,
      color: 'text-blue-500'
    }
  ];

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm relative z-50">
      <div className="max-w-full mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          
          {/* Logo et Branding Enterprise */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
                <div className="w-6 h-6 bg-white rounded-md flex items-center justify-center">
                  <div className="w-3 h-3 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-sm"></div>
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 tracking-tight">TuniBankX</h1>
                <p className="text-xs text-gray-500 font-medium">Enterprise Banking Platform</p>
              </div>
            </div>
            
            {/* Indicateurs de Statut Enterprise */}
            <div className="hidden lg:flex items-center space-x-4 ml-8">
              <div className="flex items-center space-x-2 px-3 py-1.5 bg-green-50 rounded-lg border border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-700 text-xs font-medium">Système Opérationnel</span>
              </div>
              <div className="flex items-center space-x-2 px-3 py-1.5 bg-blue-50 rounded-lg border border-blue-200">
                <Zap className="w-3 h-3 text-blue-600" />
                <span className="text-blue-700 text-xs font-medium">IA Active</span>
              </div>
              <div className="flex items-center space-x-2 px-3 py-1.5 bg-purple-50 rounded-lg border border-purple-200">
                <Shield className="w-3 h-3 text-purple-600" />
                <span className="text-purple-700 text-xs font-medium">Blockchain Sécurisé</span>
              </div>
            </div>
          </div>

          {/* Barre de Recherche Enterprise */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-12 pr-4 py-2.5 border border-gray-300 rounded-xl bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white transition-all duration-200 text-sm placeholder-gray-500"
                placeholder="Rechercher clients, transactions, rapports..."
              />
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                <kbd className="inline-flex items-center px-2 py-1 border border-gray-200 rounded text-xs font-mono text-gray-500 bg-white">
                  ⌘K
                </kbd>
              </div>
            </div>
          </div>

          {/* Actions et Profil Enterprise */}
          <div className="flex items-center space-x-4">
            
            {/* Métriques Temps Réel */}
            <div className="hidden xl:flex items-center space-x-6 mr-6">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">€2.4M</div>
                <div className="text-xs text-gray-500">Crédits Aujourd'hui</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">+12.5%</div>
                <div className="text-xs text-gray-500">Performance</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">247</div>
                <div className="text-xs text-gray-500">Clients Actifs</div>
              </div>
            </div>

            {/* Bouton de Recherche Mobile */}
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="md:hidden p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Search className="w-5 h-5" />
            </button>

            {/* Notifications Enterprise */}
            <div className="relative">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Bell className="w-5 h-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* Dropdown Notifications */}
              {showNotifications && (
                <div className="absolute right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-gray-200 py-2 z-50">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-semibold text-gray-900">Notifications</h3>
                      <span className="text-xs text-gray-500">3 nouvelles</span>
                    </div>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {notifications.map((notification) => {
                      const Icon = notification.icon;
                      return (
                        <div key={notification.id} className="px-4 py-3 hover:bg-gray-50 border-b border-gray-50 last:border-b-0">
                          <div className="flex items-start space-x-3">
                            <div className={`p-2 rounded-lg bg-gray-100 ${notification.color}`}>
                              <Icon className="w-4 h-4" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                              <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                              <p className="text-xs text-gray-400 mt-1 flex items-center">
                                <Clock className="w-3 h-3 mr-1" />
                                Il y a {notification.time}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div className="px-4 py-3 border-t border-gray-100">
                    <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                      Voir toutes les notifications
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Paramètres */}
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
              <Settings className="w-5 h-5" />
            </button>

            {/* Profil Utilisateur Enterprise */}
            <div className="relative">
              <button
                onClick={() => setShowProfile(!showProfile)}
                className="flex items-center space-x-3 p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {user?.prenom?.charAt(0)}{user?.nom?.charAt(0)}
                  </span>
                </div>
                <div className="hidden lg:block text-left">
                  <div className="text-sm font-medium text-gray-900">
                    {user?.prenom} {user?.nom}
                  </div>
                  <div className="text-xs text-gray-500 capitalize">
                    {user?.role?.replace('_', ' ')}
                  </div>
                </div>
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>

              {/* Dropdown Profil */}
              {showProfile && (
                <div className="absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-2xl border border-gray-200 py-2 z-50">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                        <span className="text-white font-semibold">
                          {user?.prenom?.charAt(0)}{user?.nom?.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          {user?.prenom} {user?.nom}
                        </div>
                        <div className="text-xs text-gray-500 capitalize">
                          {user?.role?.replace('_', ' ')}
                        </div>
                        <div className="flex items-center mt-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-xs text-green-600 font-medium">En ligne</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="py-2">
                    <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3">
                      <User className="w-4 h-4 text-gray-400" />
                      <span>Mon Profil</span>
                    </button>
                    <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3">
                      <Settings className="w-4 h-4 text-gray-400" />
                      <span>Paramètres</span>
                    </button>
                    <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3">
                      <Shield className="w-4 h-4 text-gray-400" />
                      <span>Sécurité</span>
                    </button>
                    <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-3">
                      <Globe className="w-4 h-4 text-gray-400" />
                      <span>Langue & Région</span>
                    </button>
                  </div>
                  
                  <div className="border-t border-gray-100 py-2">
                    <button
                      onClick={onLogout}
                      className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-3"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>Déconnexion</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Barre de Recherche Mobile */}
        {showSearch && (
          <div className="md:hidden py-3 border-t border-gray-200">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder="Rechercher..."
              />
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default EnterpriseHeader;
