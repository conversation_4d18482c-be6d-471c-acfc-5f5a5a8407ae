import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AuthService, AuthState } from '../services/authService';
import { User } from '../data/database';

interface AuthContextType extends AuthState {
  login: (usernameOrUserData: string | any, pinCode?: string) => Promise<boolean>;
  loginWithFaceID: (faceData: string) => Promise<boolean>;
  logout: () => void;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: false,
    error: null
  });

  const authService = AuthService.getInstance();

  useEffect(() => {
    // S'abonner aux changements d'état d'authentification
    const unsubscribe = authService.subscribe((newState: AuthState) => {
      setAuthState(newState);
    });

    // Charger l'état initial
    setAuthState(authService.getState());

    return unsubscribe;
  }, []);

  const login = async (usernameOrUserData: string | any, pinCode?: string): Promise<boolean> => {
    // Si c'est un objet utilisateur (biométrie), connecter directement
    if (typeof usernameOrUserData === 'object' && usernameOrUserData.id) {
      setAuthState({
        isAuthenticated: true,
        user: usernameOrUserData,
        loading: false,
        error: null
      });
      return true;
    }

    // Sinon, authentification traditionnelle
    if (typeof usernameOrUserData === 'string' && pinCode) {
      const success = await authService.authenticateWithPIN(usernameOrUserData, pinCode);
      if (success) {
        setAuthState(authService.getAuthState());
      }
      return success;
    }

    return false;
  };

  const loginWithFaceID = async (faceData: string): Promise<boolean> => {
    return await authService.authenticateWithFaceID(faceData);
  };

  const logout = (): void => {
    authService.logout();
  };

  const clearError = (): void => {
    authService.clearError();
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    loginWithFaceID,
    logout,
    clearError
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook personnalisé pour vérifier les permissions
export const usePermissions = () => {
  const { user } = useAuth();
  const authService = AuthService.getInstance();

  const hasPermission = (requiredRole: string): boolean => {
    return authService.hasPermission(requiredRole);
  };

  const canAccessModule = (module: string): boolean => {
    if (!user) return false;

    const modulePermissions: Record<string, string> = {
      'dashboard': 'agent',
      'clients': 'agent',
      'credits': 'analyste',
      'analytics': 'analyste',
      'documents': 'analyste',
      'security': 'directeur_credit',
      'settings': 'directeur_credit',
      'help': 'agent'
    };

    const requiredRole = modulePermissions[module] || 'directeur_credit';
    return hasPermission(requiredRole);
  };

  return {
    hasPermission,
    canAccessModule,
    userRole: user?.role || null
  };
};
