import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import UltraModernLogin from './components/UltraModernLogin';
import { 
  BarChart3, Users, CreditCard, Brain, Settings, 
  Bell, Search, LogOut, Home, TrendingUp, Shield,
  Zap, Award, Globe, FileText, PieChart
} from 'lucide-react';

// Header simplifié mais moderne
const SimpleHeader: React.FC<{ user: any; onLogout: () => void }> = ({ user, onLogout }) => {
  return (
    <header className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-full mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center">
              <div className="w-6 h-6 bg-white rounded-md flex items-center justify-center">
                <div className="w-3 h-3 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-sm"></div>
              </div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">TuniBankX Enterprise</h1>
              <p className="text-xs text-gray-500">Plateforme Bancaire Révolutionnaire</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden lg:flex items-center space-x-6">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">€2.4M</div>
                <div className="text-xs text-gray-500">Crédits</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">+12.5%</div>
                <div className="text-xs text-gray-500">Performance</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-semibold">
                  {user?.prenom?.charAt(0)}{user?.nom?.charAt(0)}
                </span>
              </div>
              <div className="hidden lg:block">
                <div className="text-sm font-medium text-gray-900">{user?.prenom} {user?.nom}</div>
                <div className="text-xs text-gray-500">{user?.role?.replace('_', ' ')}</div>
              </div>
              <button
                onClick={onLogout}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Sidebar simplifié mais moderne
const SimpleSidebar: React.FC<{ activeTab: string; onTabChange: (tab: string) => void }> = ({ activeTab, onTabChange }) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard Exécutif', icon: BarChart3, badge: 'Live' },
    { id: 'clients', label: 'Gestion Clients', icon: Users, badge: '1,247' },
    { id: 'credits', label: 'Portefeuille Crédits', icon: CreditCard, badge: '€2.4M' },
    { id: 'analytics', label: 'Technologies IA', icon: Brain, badge: 'AI' },
    { id: 'documents', label: 'Documents IA', icon: FileText, badge: 'Auto' },
    { id: 'reports', label: 'Rapports', icon: PieChart, badge: 'PDF' },
    { id: 'settings', label: 'Paramètres', icon: Settings, badge: null }
  ];

  return (
    <aside className="w-64 bg-white border-r border-gray-200 shadow-sm flex flex-col h-screen">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Award className="w-8 h-8 text-blue-600" />
          <div>
            <h2 className="text-lg font-bold text-gray-900">Centre de Contrôle</h2>
            <p className="text-sm text-gray-500">Enterprise Platform</p>
          </div>
        </div>
      </div>

      <nav className="flex-1 overflow-y-auto py-4">
        <div className="px-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={`w-full flex items-center justify-between px-4 py-3 text-sm rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                  <span className="font-medium">{item.label}</span>
                </div>
                {item.badge && (
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700">
                    {item.badge}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </nav>

      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600">Statut Système</span>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-600 font-medium">Opérationnel</span>
          </div>
        </div>
        <div className="text-center">
          <span className="text-xs text-gray-400">TuniBankX Enterprise v2.4.1</span>
        </div>
      </div>
    </aside>
  );
};

// Contenu principal
const MainContent: React.FC<{ activeTab: string }> = ({ activeTab }) => {
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div className="p-6">
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Exécutif</h2>
              <p className="text-gray-600">Vue d'ensemble de la performance bancaire en temps réel</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Clients Actifs</p>
                    <p className="text-3xl font-bold text-gray-900">1,247</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-600" />
                </div>
                <p className="text-sm text-green-600 mt-2">+12% ce mois</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Crédits Accordés</p>
                    <p className="text-3xl font-bold text-gray-900">€2.4M</p>
                  </div>
                  <CreditCard className="w-8 h-8 text-green-600" />
                </div>
                <p className="text-sm text-green-600 mt-2">+25% ce mois</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Taux de Défaut</p>
                    <p className="text-3xl font-bold text-gray-900">2.1%</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-purple-600" />
                </div>
                <p className="text-sm text-red-600 mt-2">-40% vs industrie</p>
              </div>
              
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Score IA</p>
                    <p className="text-3xl font-bold text-gray-900">96%</p>
                  </div>
                  <Brain className="w-8 h-8 text-indigo-600" />
                </div>
                <p className="text-sm text-green-600 mt-2">Précision optimale</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Technologies Actives</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Quantum AI Risk Engine</span>
                    <span className="text-sm font-medium text-green-600">99.2% Précision</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Goldman Sachs ML Scoring</span>
                    <span className="text-sm font-medium text-green-600">Actif</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Hyperledger Blockchain</span>
                    <span className="text-sm font-medium text-green-600">10,000 TPS</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">GPT-4 Assistant</span>
                    <span className="text-sm font-medium text-green-600">24/7 Actif</span>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes Système</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Tous les systèmes opérationnels</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">IA en apprentissage continu</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Blockchain synchronisée</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'clients':
        return (
          <div className="p-6">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Gestion Clients Avancée</h2>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200">
              <p className="text-gray-600 mb-4">Module de gestion clients avec IA de scoring en temps réel</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-800">Clients Premium</h4>
                  <p className="text-2xl font-bold text-blue-600">342</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800">Nouveaux Clients</h4>
                  <p className="text-2xl font-bold text-green-600">89</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-800">Score Moyen IA</h4>
                  <p className="text-2xl font-bold text-purple-600">87/100</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'credits':
        return (
          <div className="p-6">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Portefeuille Crédits</h2>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200">
              <p className="text-gray-600 mb-4">Gestion automatisée des crédits avec scoring IA prédictif</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800">Crédits Approuvés</h4>
                  <p className="text-2xl font-bold text-green-600">€2.4M</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-orange-800">En Attente</h4>
                  <p className="text-2xl font-bold text-orange-600">€450K</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-red-800">Taux Défaut</h4>
                  <p className="text-2xl font-bold text-red-600">2.1%</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'analytics':
        return (
          <div className="p-6">
            <div className="bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 text-white p-8 rounded-2xl mb-8">
              <h2 className="text-3xl font-bold mb-4">Technologies IA Enterprise</h2>
              <p className="text-blue-100 mb-6">Technologies de niveau Goldman Sachs et JPMorgan Chase</p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4">
                  <div className="text-2xl font-bold text-blue-300">15+</div>
                  <div className="text-blue-100 text-sm">Technologies IA</div>
                </div>
                <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4">
                  <div className="text-2xl font-bold text-green-300">99.99%</div>
                  <div className="text-green-100 text-sm">Disponibilité</div>
                </div>
                <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4">
                  <div className="text-2xl font-bold text-purple-300">24/7</div>
                  <div className="text-purple-100 text-sm">Support IA</div>
                </div>
                <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4">
                  <div className="text-2xl font-bold text-orange-300">&lt; 100ms</div>
                  <div className="text-orange-100 text-sm">Latence</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { title: 'Quantum AI Risk Engine', desc: 'Moteur quantique JPMorgan', status: 'Actif', color: 'blue' },
                { title: 'Goldman Sachs ML', desc: 'Scoring validé $2.5T', status: 'Actif', color: 'green' },
                { title: 'GPT-4 Assistant', desc: 'IA conversationnelle avancée', status: 'Actif', color: 'purple' },
                { title: 'Hyperledger Blockchain', desc: '10,000 TPS enterprise', status: 'Actif', color: 'indigo' },
                { title: 'Bloomberg Integration', desc: 'Données 40+ marchés', status: 'Actif', color: 'orange' },
                { title: 'Quantum Cryptography', desc: 'Sécurité post-quantique', status: 'Beta', color: 'red' }
              ].map((tech, index) => (
                <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{tech.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{tech.desc}</p>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium bg-${tech.color}-100 text-${tech.color}-700`}>
                    {tech.status}
                  </span>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div className="p-6">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">{activeTab}</h2>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200">
              <p className="text-gray-600">Module en développement...</p>
            </div>
          </div>
        );
    }
  };

  return <main className="flex-1 bg-gray-50">{renderContent()}</main>;
};

// App principale
const WorkingMainApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { user, logout } = useAuth();

  if (!user) {
    return <UltraModernLogin />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <SimpleHeader user={user} onLogout={logout} />
      <div className="flex">
        <SimpleSidebar activeTab={activeTab} onTabChange={setActiveTab} />
        <MainContent activeTab={activeTab} />
      </div>
    </div>
  );
};

// App avec AuthProvider
const WorkingApp: React.FC = () => {
  return (
    <AuthProvider>
      <WorkingMainApp />
    </AuthProvider>
  );
};

export default WorkingApp;
