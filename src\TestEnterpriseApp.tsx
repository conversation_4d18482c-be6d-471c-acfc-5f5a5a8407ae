import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import UltraModernLogin from './components/UltraModernLogin';

// Test simple pour identifier le problème
const TestEnterpriseMainApp: React.FC = () => {
  const { user, logout } = useAuth();

  if (!user) {
    return <UltraModernLogin />;
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🏦 TuniBankX Enterprise - Test
          </h1>
          <p className="text-gray-600 mb-6">
            Bienvenue, {user?.prenom} {user?.nom} ({user?.role})
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-blue-800 mb-2">✅ Authentification</h3>
              <p className="text-blue-600">Système fonctionnel</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-green-800 mb-2">✅ Base de Données</h3>
              <p className="text-green-600">Connexion établie</p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-purple-800 mb-2">✅ Interface</h3>
              <p className="text-purple-600">Chargement réussi</p>
            </div>
          </div>

          <div className="space-y-4">
            <button className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
              📊 Tester Dashboard
            </button>
            <button className="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors">
              👥 Tester Gestion Clients
            </button>
            <button className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg hover:bg-purple-700 transition-colors">
              🤖 Tester Technologies IA
            </button>
            <button 
              onClick={logout}
              className="w-full bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 transition-colors"
            >
              🚪 Déconnexion
            </button>
          </div>

          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">🔧 Informations de Debug</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• React: ✅ Fonctionnel</p>
              <p>• TypeScript: ✅ Compilé</p>
              <p>• Tailwind CSS: ✅ Chargé</p>
              <p>• Authentification: ✅ Active</p>
              <p>• Utilisateur: {JSON.stringify(user, null, 2)}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// App de test avec AuthProvider
const TestEnterpriseApp: React.FC = () => {
  return (
    <AuthProvider>
      <TestEnterpriseMainApp />
    </AuthProvider>
  );
};

export default TestEnterpriseApp;
