// Service d'authentification pour TuniBankX
import { User, DatabaseService } from '../data/database';

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username?: string;
  pinCode?: string;
  faceData?: string;
}

export class AuthService {
  private static instance: AuthService;
  private db: DatabaseService;
  private authState: AuthState = {
    isAuthenticated: false,
    user: null,
    loading: false,
    error: null
  };
  private listeners: ((state: AuthState) => void)[] = [];

  constructor() {
    this.db = DatabaseService.getInstance();
    this.loadStoredAuth();
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Gestion des listeners pour les changements d'état
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.authState));
  }

  private updateState(updates: Partial<AuthState>): void {
    this.authState = { ...this.authState, ...updates };
    this.notifyListeners();
  }

  // Chargement de l'authentification stockée
  private loadStoredAuth(): void {
    try {
      const storedAuth = localStorage.getItem('tunibank_auth');
      if (storedAuth) {
        const { userId, timestamp } = JSON.parse(storedAuth);
        const now = Date.now();
        const sessionDuration = 24 * 60 * 60 * 1000; // 24 heures

        if (now - timestamp < sessionDuration) {
          const user = this.db.getUserById(userId);
          if (user && user.isActive) {
            this.updateState({
              isAuthenticated: true,
              user,
              error: null
            });
          }
        } else {
          localStorage.removeItem('tunibank_auth');
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'authentification:', error);
      localStorage.removeItem('tunibank_auth');
    }
  }

  // Sauvegarde de l'authentification
  private saveAuth(user: User): void {
    const authData = {
      userId: user.id,
      timestamp: Date.now()
    };
    localStorage.setItem('tunibank_auth', JSON.stringify(authData));
  }

  // Authentification par Face ID
  async authenticateWithFaceID(faceData: string): Promise<boolean> {
    this.updateState({ loading: true, error: null });

    try {
      // Simulation de la vérification biométrique
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Dans un vrai système, on comparerait les données biométriques
      // Ici, on simule une authentification réussie pour l'utilisateur par défaut
      const user = this.db.getUserByUsername('directeur.credit');
      
      if (user && user.isActive) {
        // Mise à jour de la dernière connexion
        this.db.updateUser(user.id, { lastLogin: new Date().toISOString() });
        
        this.saveAuth(user);
        this.updateState({
          isAuthenticated: true,
          user,
          loading: false,
          error: null
        });
        return true;
      } else {
        throw new Error('Utilisateur non trouvé ou inactif');
      }
    } catch (error) {
      this.updateState({
        loading: false,
        error: 'Échec de l\'authentification biométrique'
      });
      return false;
    }
  }

  // Authentification par PIN
  async authenticateWithPIN(username: string, pinCode: string): Promise<boolean> {
    this.updateState({ loading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const user = this.db.getUserByUsername(username);
      
      if (user && user.isActive && user.pinCode === pinCode) {
        this.db.updateUser(user.id, { lastLogin: new Date().toISOString() });
        
        this.saveAuth(user);
        this.updateState({
          isAuthenticated: true,
          user,
          loading: false,
          error: null
        });
        return true;
      } else {
        throw new Error('Nom d\'utilisateur ou PIN incorrect');
      }
    } catch (error) {
      this.updateState({
        loading: false,
        error: error instanceof Error ? error.message : 'Erreur d\'authentification'
      });
      return false;
    }
  }

  // Déconnexion
  logout(): void {
    localStorage.removeItem('tunibank_auth');
    this.updateState({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null
    });
  }

  // Obtenir l'état actuel
  getState(): AuthState {
    return this.authState;
  }

  // Vérifier si l'utilisateur est authentifié
  isAuthenticated(): boolean {
    return this.authState.isAuthenticated;
  }

  // Obtenir l'utilisateur actuel
  getCurrentUser(): User | null {
    return this.authState.user;
  }

  // Enregistrer des données biométriques pour un utilisateur
  async registerFaceData(userId: string, faceData: string): Promise<boolean> {
    try {
      const success = this.db.updateUser(userId, { faceData });
      return success;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des données biométriques:', error);
      return false;
    }
  }

  // Vérifier les permissions d'accès
  hasPermission(requiredRole: string): boolean {
    if (!this.authState.user) return false;
    
    const roleHierarchy = {
      'agent': 1,
      'analyste': 2,
      'directeur_credit': 3
    };

    const userLevel = roleHierarchy[this.authState.user.role as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

    return userLevel >= requiredLevel;
  }

  // Réinitialiser l'erreur
  clearError(): void {
    this.updateState({ error: null });
  }
}
