import React from 'react';
import { Bell, Search, User, Settings, LogOut } from 'lucide-react';

interface HeaderProps {
  currentUser: string;
  onLogout?: () => void;
}

const Header: React.FC<HeaderProps> = ({ currentUser, onLogout }) => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">TB</span>
          </div>
          <h1 className="text-xl font-bold text-gray-900">TuniBankX</h1>
        </div>
        
        <div className="hidden md:flex items-center bg-gray-50 rounded-lg px-3 py-2 ml-8">
          <Search className="w-4 h-4 text-gray-400 mr-2" />
          <input
            type="text"
            placeholder="Rechercher un client, contrat..."
            className="bg-transparent border-none outline-none text-sm text-gray-700 w-64"
          />
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative">
          <Bell className="w-5 h-5 text-gray-600 hover:text-blue-600 cursor-pointer transition-colors" />
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
        </div>
        
        <div className="flex items-center space-x-2 border-l border-gray-200 pl-4">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-blue-600" />
          </div>
          <div className="hidden md:block">
            <p className="text-sm font-medium text-gray-900">{currentUser}</p>
            <p className="text-xs text-gray-500">Directeur Crédit</p>
          </div>
          <div className="flex items-center space-x-2 ml-2">
            <Settings className="w-4 h-4 text-gray-400 hover:text-blue-600 cursor-pointer transition-colors" />
            {onLogout && (
              <button
                onClick={onLogout}
                className="w-4 h-4 text-gray-400 hover:text-red-600 cursor-pointer transition-colors"
                title="Se déconnecter"
              >
                <LogOut className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;