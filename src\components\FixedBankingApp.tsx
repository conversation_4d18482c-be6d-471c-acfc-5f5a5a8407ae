import React, { useState, useEffect } from 'react';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import UltraModernLogin from './UltraModernLogin';
import BiometricAuth from './BiometricAuth';
import {
  Home, BarChart3, Users, CreditCard, Brain, Settings,
  Bell, Search, LogOut, User, Shield, TrendingUp,
  DollarSign, Plus, Download, Eye, ArrowUpRight, ArrowDownRight,
  FileText, PieChart, Smartphone, Globe, Lock, Zap,
  Activity, Target, Award, Star, Calendar, Filter, Cloud, Cpu, Database, Wifi, Camera
} from 'lucide-react';

// Header Premium avec tous les éléments demandés
const PremiumBankingHeader: React.FC<{ user: any; onLogout: () => void }> = ({ user, onLogout }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedLanguage, setSelectedLanguage] = useState('fr');
  const [notificationCount, setNotificationCount] = useState(3);

  // Mise à jour de l'horloge chaque seconde
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const languages = [
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'ar', name: 'العربية', flag: '🇹🇳' },
    { code: 'en', name: 'English', flag: '🇬🇧' }
  ];

  return (
    <header className="bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 border-b border-blue-800 px-6 py-4 shadow-2xl">
      <div className="flex items-center justify-between">
        {/* Logo Animé Premium */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 via-cyan-500 to-teal-400 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-all duration-300 animate-pulse">
              <div className="w-8 h-8 bg-white rounded-xl flex items-center justify-center">
                <div className="w-5 h-5 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-lg"></div>
              </div>
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-ping"></div>
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              TuniBankX Enterprise
            </h1>
            <p className="text-sm text-blue-300 font-medium">Plateforme Bancaire IA Mondiale</p>
          </div>
        </div>

        {/* Horloge Digitale + Date */}
        <div className="hidden lg:flex items-center space-x-6">
          <div className="bg-black/30 backdrop-blur-sm rounded-xl px-4 py-2 border border-blue-500/30">
            <div className="text-center">
              <div className="text-2xl font-mono font-bold text-cyan-400">
                {currentTime.toLocaleTimeString('fr-FR', { hour12: false })}
              </div>
              <div className="text-xs text-blue-300 font-medium">
                {currentTime.toLocaleDateString('fr-FR', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Recherche Premium */}
        <div className="flex-1 max-w-2xl mx-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400" />
            <input
              type="text"
              placeholder="Recherche intelligente : clients, transactions, dossiers..."
              className="w-full pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-blue-500/30 rounded-xl text-white placeholder-blue-300 focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 transition-all duration-300"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <kbd className="px-2 py-1 text-xs bg-blue-500/30 text-blue-300 rounded border border-blue-400/30">
                Ctrl+K
              </kbd>
            </div>
          </div>
        </div>

        {/* Actions Utilisateur Premium */}
        <div className="flex items-center space-x-4">
          {/* Sélecteur de Langue */}
          <div className="relative group">
            <button className="flex items-center space-x-2 px-3 py-2 bg-white/10 backdrop-blur-sm border border-blue-500/30 rounded-lg hover:bg-white/20 transition-all duration-300">
              <span className="text-lg">{languages.find(l => l.code === selectedLanguage)?.flag}</span>
              <span className="text-white text-sm font-medium hidden md:block">
                {languages.find(l => l.code === selectedLanguage)?.name}
              </span>
            </button>
            <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-2xl border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
              {languages.map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => setSelectedLanguage(lang.code)}
                  className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-blue-50 first:rounded-t-xl last:rounded-b-xl transition-colors"
                >
                  <span className="text-lg">{lang.flag}</span>
                  <span className="text-gray-900 font-medium">{lang.name}</span>
                  {selectedLanguage === lang.code && (
                    <CheckCircle className="w-4 h-4 text-blue-600 ml-auto" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Notifications avec Badge */}
          <button className="relative p-3 bg-white/10 backdrop-blur-sm border border-blue-500/30 rounded-lg hover:bg-white/20 transition-all duration-300 group">
            <Bell className="w-5 h-5 text-blue-300 group-hover:text-white transition-colors" />
            {notificationCount > 0 && (
              <span className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-bounce">
                {notificationCount}
              </span>
            )}
          </button>

          {/* Profil Utilisateur Premium */}
          <div className="flex items-center space-x-3 pl-4 border-l border-blue-500/30">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ring-2 ring-blue-400/50 ring-offset-2 ring-offset-transparent">
                <span className="text-white text-lg font-bold">
                  LS
                </span>
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
            </div>
            <div className="hidden md:block">
              <div className="text-white font-semibold">Lassad Sniha</div>
              <div className="text-blue-300 text-sm">Analyste BI Senior</div>
            </div>
          </div>

          {/* Bouton Déconnexion Premium */}
          <button
            onClick={onLogout}
            className="group flex items-center space-x-2 px-4 py-2 bg-red-500/20 border border-red-500/30 text-red-300 hover:bg-red-500 hover:text-white rounded-lg transition-all duration-300 relative overflow-hidden"
            title="Déconnexion sécurisée"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <LogOut className="w-4 h-4 relative z-10" />
            <span className="text-sm font-medium relative z-10 hidden lg:block">Déconnexion</span>
          </button>
        </div>
      </div>
    </header>
  );
};

// Sidebar Premium avec navigation avancée
const PremiumBankingSidebar: React.FC<{ activeTab: string; onTabChange: (tab: string) => void }> = ({ activeTab, onTabChange }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const menuItems = [
    { id: 'accueil', label: 'Accueil', icon: Home, badge: null, color: 'blue' },
    { id: 'dashboard', label: 'Dashboard Multi-Utilisateurs', icon: BarChart3, badge: 'Live', color: 'green' },
    { id: 'clients', label: 'CRM Bancaire Intégré', icon: Users, badge: '1,247', color: 'purple' },
    { id: 'credits', label: 'Gestion Dossiers Crédits', icon: CreditCard, badge: '€2.4M', color: 'orange' },
    { id: 'rentabilite', label: 'Suivi Rentabilité Client', icon: TrendingUp, badge: 'ROI', color: 'emerald' },
    { id: 'performances', label: 'Performances Commerciales', icon: Target, badge: 'Agences', color: 'pink' },
    { id: 'litiges', label: 'Gestion Litiges', icon: Shield, badge: 'Incidents', color: 'red' },
    { id: 'formulaires', label: 'Formulaires Intelligents', icon: FileText, badge: 'Auto', color: 'teal' },
    { id: 'kpi', label: 'KPI Temps Réel', icon: Activity, badge: 'Live', color: 'cyan' },
    { id: 'biometrie', label: 'Authentification Biométrique', icon: Smartphone, badge: 'Face ID', color: 'indigo' },
    { id: 'ia-recommandations', label: 'IA Recommandations', icon: Brain, badge: 'Smart', color: 'violet' },
    { id: 'analyse-comportementale', label: 'Analyse Comportementale', icon: Eye, badge: 'Prédictif', color: 'amber' },
    { id: 'detection-fraude', label: 'Détection Fraude IA', icon: Lock, badge: 'Auto', color: 'rose' },
    { id: 'rapports-bi', label: 'Rapports BI Dynamiques', icon: PieChart, badge: 'BI', color: 'lime' },
    { id: 'tech-entreprises', label: 'Technologies Entreprises', icon: Zap, badge: 'GAFAM', color: 'fuchsia' },
    { id: 'parametres', label: 'Paramètres Avancés', icon: Settings, badge: null, color: 'gray' }
  ];

  return (
    <aside className={`${isCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-slate-900 via-blue-900 to-indigo-900 border-r border-blue-800 shadow-2xl flex flex-col h-screen transition-all duration-300`}>
      {/* En-tête sidebar premium */}
      <div className="p-6 border-b border-blue-800/50">
        <div className="flex items-center justify-between">
          <div className={`flex items-center space-x-3 ${isCollapsed ? 'justify-center' : ''}`}>
            <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
              <Award className="w-6 h-6 text-white" />
            </div>
            {!isCollapsed && (
              <div>
                <h2 className="text-lg font-bold text-white">Centre de Contrôle</h2>
                <p className="text-sm text-blue-300">Plateforme Enterprise</p>
              </div>
            )}
          </div>
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 text-blue-300 hover:text-white hover:bg-blue-800/50 rounded-lg transition-all duration-200"
          >
            <Filter className="w-4 h-4" />
          </button>
        </div>

        {/* Statut système premium */}
        {!isCollapsed && (
          <div className="mt-4 p-4 bg-black/20 backdrop-blur-sm rounded-xl border border-blue-500/30">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-blue-200">Statut Système</span>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                <span className="text-sm text-green-400 font-semibold">Opérationnel</span>
              </div>
            </div>
            <div className="text-xs text-blue-300 mb-2">Toutes les IA actives • Latence: &lt; 50ms</div>
            <div className="flex items-center space-x-4 text-xs">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                <span className="text-cyan-300">Quantum AI</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span className="text-purple-300">Blockchain</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Premium */}
      <nav className="flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-blue-600 scrollbar-track-blue-900/20">
        <div className="px-3 space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;

            return (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={`group w-full flex items-center ${isCollapsed ? 'justify-center px-3' : 'justify-between px-4'} py-4 text-sm rounded-xl transition-all duration-300 relative overflow-hidden ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-white border border-blue-400/30 shadow-lg shadow-blue-500/20'
                    : 'text-blue-200 hover:text-white hover:bg-blue-800/30'
                }`}
                title={isCollapsed ? item.label : ''}
              >
                {/* Active indicator */}
                {isActive && (
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-cyan-400 to-blue-500 rounded-r-full"></div>
                )}

                <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>
                  <Icon className={`w-5 h-5 transition-all duration-300 ${
                    isActive ? 'text-cyan-400' : 'text-blue-300 group-hover:text-white'
                  }`} />
                  {!isCollapsed && (
                    <span className="font-medium transition-colors duration-300">{item.label}</span>
                  )}
                </div>

                {!isCollapsed && item.badge && (
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full transition-all duration-300 ${
                    isActive
                      ? 'bg-cyan-400/20 text-cyan-300 border border-cyan-400/30'
                      : 'bg-blue-700/50 text-blue-300 group-hover:bg-blue-600/50 group-hover:text-white'
                  }`}>
                    {item.badge}
                  </span>
                )}

                {/* Hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
              </button>
            );
          })}
        </div>
      </nav>

      {/* Footer sidebar */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <div className="text-lg font-bold text-blue-600">€2.4M</div>
            <div className="text-xs text-gray-500">Portefeuille</div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <div className="text-lg font-bold text-green-600">96%</div>
            <div className="text-xs text-gray-500">Précision IA</div>
          </div>
        </div>
        <div className="text-center">
          <span className="text-xs text-gray-400">TuniBankX Enterprise v3.0.1</span>
        </div>
      </div>
    </aside>
  );
};

// Page d'accueil Premium de niveau banque mondiale
const PremiumAccueilPage: React.FC = () => {
  const [animatedValues, setAnimatedValues] = useState({
    dossiers: 0,
    traites: 0,
    rdv: 0,
    temps: 0,
    montant: 0
  });

  // Animation des compteurs au chargement
  useEffect(() => {
    const targets = {
      dossiers: 154,
      traites: 32,
      rdv: 6,
      temps: 12,
      montant: 1.3
    };

    const duration = 2000; // 2 secondes
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;

      setAnimatedValues({
        dossiers: Math.floor(targets.dossiers * progress),
        traites: Math.floor(targets.traites * progress),
        rdv: Math.floor(targets.rdv * progress),
        temps: Math.floor(targets.temps * progress),
        montant: parseFloat((targets.montant * progress).toFixed(1))
      });

      if (currentStep >= steps) {
        clearInterval(timer);
        setAnimatedValues(targets);
      }
    }, stepDuration);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Hero Section Premium */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-900 via-indigo-900 to-purple-900 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }}></div>
        </div>

        <div className="relative px-8 py-16">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <div className="inline-flex items-center space-x-3 mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-2xl">
                  <Home className="w-8 h-8 text-white" />
                </div>
                <div className="text-left">
                  <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
                    TuniBankX Enterprise
                  </h1>
                  <p className="text-cyan-300 text-lg font-medium">Dashboard Exécutif - Niveau Banque Mondiale</p>
                </div>
              </div>

              <p className="text-xl text-blue-200 max-w-4xl mx-auto leading-relaxed">
                Plateforme bancaire révolutionnaire avec technologies IA de Goldman Sachs, JPMorgan Chase et Deutsche Bank
              </p>

              <div className="flex items-center justify-center space-x-8 mt-8">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-300 font-medium">Système Opérationnel</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-blue-300 font-medium">IA Active</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                  <span className="text-purple-300 font-medium">Sécurité Maximale</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard KPI Premium avec animations */}
      <div className="px-8 py-12">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Tableau de Bord Synthétique</h2>
            <p className="text-gray-600">Métriques temps réel avec intelligence artificielle</p>
          </div>

          {/* Cartes KPI Animées */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
            {[
              {
                icon: '🔁',
                title: 'Dossiers en cours',
                value: animatedValues.dossiers,
                suffix: '',
                variation: '+5%',
                color: 'blue',
                bgGradient: 'from-blue-500 to-cyan-500'
              },
              {
                icon: '✅',
                title: 'Dossiers traités',
                value: animatedValues.traites,
                suffix: '',
                variation: '+12%',
                color: 'green',
                bgGradient: 'from-green-500 to-emerald-500'
              },
              {
                icon: '📅',
                title: 'Rendez-vous prévus',
                value: animatedValues.rdv,
                suffix: '',
                variation: '—',
                color: 'purple',
                bgGradient: 'from-purple-500 to-violet-500'
              },
              {
                icon: '⏱️',
                title: 'Temps moyen traitement',
                value: animatedValues.temps,
                suffix: ' min',
                variation: '-2%',
                color: 'orange',
                bgGradient: 'from-orange-500 to-amber-500'
              },
              {
                icon: '💰',
                title: 'Montant total financé',
                value: animatedValues.montant,
                suffix: 'M TND',
                variation: '+7%',
                color: 'emerald',
                bgGradient: 'from-emerald-500 to-teal-500'
              }
            ].map((kpi, index) => (
              <div
                key={index}
                className="group relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100"
              >
                {/* Gradient Background on Hover */}
                <div className={`absolute inset-0 bg-gradient-to-br ${kpi.bgGradient} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-500`}></div>

                {/* Icon */}
                <div className="flex items-center justify-between mb-4">
                  <div className="text-3xl">{kpi.icon}</div>
                  <span className={`text-sm font-semibold px-2 py-1 rounded-full ${
                    kpi.variation.startsWith('+') ? 'bg-green-100 text-green-700' :
                    kpi.variation.startsWith('-') ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {kpi.variation}
                  </span>
                </div>

                {/* Value */}
                <div className="mb-2">
                  <span className="text-3xl font-bold text-gray-900">
                    {kpi.value}
                  </span>
                  <span className="text-lg text-gray-600 ml-1">{kpi.suffix}</span>
                </div>

                {/* Title */}
                <div className="text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors">
                  {kpi.title}
                </div>

                {/* Hover Effect Line */}
                <div className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${kpi.bgGradient} w-0 group-hover:w-full transition-all duration-500 rounded-b-2xl`}></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

      {/* Technologies showcase */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">Technologies Ultra-Avancées</h2>
          <p className="text-blue-100 text-lg">
            Technologies des plus grandes banques mondiales - Inexistantes en Tunisie
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            { name: 'IBM Quantum System One', desc: '1000+ Qubits Superconducteurs', icon: Brain, bank: 'JPMorgan Chase', level: 'Quantique' },
            { name: 'NVIDIA DGX H100', desc: '32 PetaFLOPS IA', icon: Zap, bank: 'Goldman Sachs', level: 'Supercomputer' },
            { name: 'Bloomberg Terminal BPIPE', desc: '40+ Marchés Temps Réel', icon: Globe, bank: 'Toutes Banques', level: 'Enterprise' },
            { name: 'Apple M2 Ultra Secure', desc: 'Cryptage Niveau NSA', icon: Lock, bank: 'Bank of America', level: 'Militaire' },
            { name: 'Google TPU v5 Pods', desc: '1.1 ExaFLOPS ML', icon: Activity, bank: 'Deutsche Bank', level: 'IA Avancée' },
            { name: 'Microsoft Azure Quantum', desc: 'Cloud Quantique Hybride', icon: Cloud, bank: 'HSBC', level: 'Cloud Quantique' },
            { name: 'AWS Trainium2', desc: 'Puces IA Custom Amazon', icon: Cpu, bank: 'Citibank', level: 'Custom Silicon' },
            { name: 'Tesla Dojo v2', desc: 'Neural Network Training', icon: Target, bank: 'Morgan Stanley', level: 'Neuromorphique' },
            { name: 'Meta Reality Labs', desc: 'Metaverse Banking VR/AR', icon: Eye, bank: 'Wells Fargo', level: 'Metaverse' },
            { name: 'SpaceX Starlink V2', desc: 'Internet Satellite Global', icon: Wifi, bank: 'UBS', level: 'Spatial' },
            { name: 'Palantir Foundry Gov', desc: 'Big Data CIA/NSA Level', icon: Database, bank: 'Credit Suisse', level: 'Gouvernemental' },
            { name: 'OpenAI GPT-5 Financial', desc: 'AGI pour Finance', icon: Star, bank: 'Barclays', level: 'AGI' }
          ].map((tech, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-xl rounded-xl p-4 text-center hover:bg-white/20 transition-all border border-white/20">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <tech.icon className="w-6 h-6 text-white" />
              </div>
              <div className="font-semibold text-white mb-1 text-sm">{tech.name}</div>
              <div className="text-xs text-blue-200 mb-2">{tech.desc}</div>
              <div className="text-xs text-yellow-300 mb-1">{tech.bank}</div>
              <div className="text-xs text-green-300 bg-white/10 rounded-full px-2 py-1">{tech.level}</div>
            </div>
          ))}
        </div>

        {/* Section Technologies Révolutionnaires */}
        <div className="mt-12 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">🚀 Technologies Révolutionnaires des Plus Grandes Entreprises Mondiales</h3>
            <p className="text-purple-100 text-lg">
              Technologies de pointe utilisées par Apple, Google, Microsoft, Amazon, Tesla, Meta - Niveau Science-Fiction
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                name: 'Apple Neural Engine M3',
                desc: 'Puce neuromorphique 15.8 TOPS pour IA on-device',
                company: 'Apple Inc.',
                specs: ['15.8 TOPS', '3nm Process', 'On-device ML', 'Privacy-first'],
                usage: 'Authentification biométrique ultra-sécurisée',
                level: 'Production'
              },
              {
                name: 'Google Quantum AI Sycamore',
                desc: 'Processeur quantique 70 qubits avec suprématie quantique',
                company: 'Google/Alphabet',
                specs: ['70 Qubits', 'Suprématie quantique', 'Error correction', 'Cloud accessible'],
                usage: 'Optimisation portefeuille et cryptographie',
                level: 'Recherche'
              },
              {
                name: 'Microsoft HoloLens 3',
                desc: 'Réalité mixte pour banking immersif et visualisation 3D',
                company: 'Microsoft Corp.',
                specs: ['8K per eye', 'Hand tracking', 'Eye tracking', 'Spatial computing'],
                usage: 'Consultation bancaire en réalité augmentée',
                level: 'Enterprise'
              },
              {
                name: 'Amazon Graviton4',
                desc: 'Processeur ARM custom 64-core pour cloud banking',
                company: 'Amazon Web Services',
                specs: ['64 cores ARM', '40% performance', 'Energy efficient', 'Cloud native'],
                usage: 'Infrastructure bancaire cloud ultra-performante',
                level: 'Production'
              },
              {
                name: 'Tesla FSD Computer v4',
                desc: 'Puce autonome 144 TOPS pour décisions temps réel',
                company: 'Tesla Inc.',
                specs: ['144 TOPS', 'Real-time decisions', 'Neural networks', 'Edge computing'],
                usage: 'Trading algorithmique haute fréquence',
                level: 'Déploiement'
              },
              {
                name: 'Meta Reality Labs Codec',
                desc: 'Avatar photorealistic pour metaverse banking',
                company: 'Meta Platforms',
                specs: ['Photorealistic', 'Real-time rendering', 'VR/AR ready', 'Social presence'],
                usage: 'Conseillers virtuels IA dans le metaverse',
                level: 'Beta'
              },
              {
                name: 'NVIDIA Grace Hopper',
                desc: 'Superchip CPU+GPU pour IA générative financière',
                company: 'NVIDIA Corp.',
                specs: ['72 ARM cores', '96GB HBM3', '900GB/s bandwidth', 'AI optimized'],
                usage: 'Modèles IA génératifs pour analyse financière',
                level: 'Production'
              },
              {
                name: 'Intel Ponte Vecchio',
                desc: 'GPU datacenter 47 tiles pour calculs massivement parallèles',
                company: 'Intel Corp.',
                specs: ['47 tiles', 'Xe-HPC architecture', 'HBM2e memory', 'Exascale ready'],
                usage: 'Simulations Monte Carlo pour gestion des risques',
                level: 'Enterprise'
              },
              {
                name: 'AMD MI300X',
                desc: 'Accélérateur IA 192GB HBM3 pour LLM financiers',
                company: 'AMD Inc.',
                specs: ['192GB HBM3', '5.3TB/s bandwidth', 'CDNA3 architecture', 'AI inference'],
                usage: 'Large Language Models pour conseil financier',
                level: 'Production'
              },
              {
                name: 'Qualcomm Snapdragon X Elite',
                desc: 'SoC ARM avec NPU 45 TOPS pour mobile banking',
                company: 'Qualcomm Inc.',
                specs: ['45 TOPS NPU', 'Oryon CPU', '5G modem', 'Always-on AI'],
                usage: 'Banking mobile avec IA embarquée',
                level: 'Commercial'
              },
              {
                name: 'IBM watsonx.ai',
                desc: 'Plateforme IA générative pour entreprise avec foundation models',
                company: 'IBM Corp.',
                specs: ['Foundation models', 'Enterprise ready', 'Hybrid cloud', 'Governance'],
                usage: 'Assistant IA conversationnel pour banque',
                level: 'Enterprise'
              },
              {
                name: 'Anthropic Claude 3 Opus',
                desc: 'LLM constitutionnel ultra-sécurisé pour finance',
                company: 'Anthropic Inc.',
                specs: ['Constitutional AI', 'Safety focused', 'Reasoning', 'Multimodal'],
                usage: 'Analyse de documents financiers complexes',
                level: 'API'
              }
            ].map((tech, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-xl rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-white text-lg">{tech.name}</h4>
                    <p className="text-purple-200 text-sm">{tech.company}</p>
                  </div>
                </div>

                <p className="text-purple-100 text-sm mb-4">{tech.desc}</p>

                <div className="mb-4">
                  <h5 className="text-white font-semibold text-sm mb-2">Spécifications:</h5>
                  <div className="grid grid-cols-2 gap-1">
                    {tech.specs.map((spec, idx) => (
                      <div key={idx} className="text-purple-200 text-xs flex items-center">
                        <div className="w-1 h-1 bg-purple-300 rounded-full mr-2"></div>
                        {spec}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mb-4">
                  <h5 className="text-white font-semibold text-sm mb-2">Usage Bancaire:</h5>
                  <p className="text-purple-200 text-xs">{tech.usage}</p>
                </div>

                <div className="flex items-center justify-between">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    tech.level === 'Production' ? 'bg-green-400/20 text-green-300' :
                    tech.level === 'Enterprise' ? 'bg-blue-400/20 text-blue-300' :
                    tech.level === 'Commercial' ? 'bg-cyan-400/20 text-cyan-300' :
                    tech.level === 'API' ? 'bg-yellow-400/20 text-yellow-300' :
                    tech.level === 'Beta' ? 'bg-orange-400/20 text-orange-300' :
                    tech.level === 'Déploiement' ? 'bg-purple-400/20 text-purple-300' :
                    'bg-pink-400/20 text-pink-300'
                  }`}>
                    {tech.level}
                  </span>
                  <button className="text-white hover:text-purple-200 text-xs font-medium bg-white/10 px-3 py-1 rounded-full">
                    Intégrer
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Fermeture des sections manquantes */}
        </div>
      </div>
    </div>
  );
};

// CRM Bancaire Intégré
const CRMBancairePage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">CRM Bancaire Intégré</h2>
          <p className="text-gray-600">Historique complet des interactions clients</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Plus className="w-4 h-4" />
            <span>Nouveau Client</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Clients par Segment</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Premium</span>
              <span className="font-semibold text-blue-600">342 clients</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Standard</span>
              <span className="font-semibold text-green-600">785 clients</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Nouveaux</span>
              <span className="font-semibold text-orange-600">120 clients</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Interactions Récentes</h3>
          <div className="space-y-3">
            {['Appel entrant', 'Email envoyé', 'RDV planifié', 'Réclamation'].map((interaction, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600">{interaction}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Score Satisfaction</h3>
          <div className="text-center">
            <div className="text-4xl font-bold text-green-600 mb-2">4.8/5</div>
            <div className="text-sm text-gray-600">Basé sur 1,247 avis</div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Liste des Clients</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Client</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Segment</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Dernière Interaction</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Score IA</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {[
                { nom: 'Ahmed Ben Ali', segment: 'Premium', interaction: 'Appel - 2h', score: '95/100' },
                { nom: 'Fatma Trabelsi', segment: 'Standard', interaction: 'Email - 1j', score: '87/100' },
                { nom: 'Mohamed Gharbi', segment: 'Premium', interaction: 'RDV - 3j', score: '92/100' }
              ].map((client, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">{client.nom}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      client.segment === 'Premium' ? 'bg-blue-100 text-blue-700' : 'bg-green-100 text-green-700'
                    }`}>
                      {client.segment}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-600">{client.interaction}</td>
                  <td className="py-3 px-4 font-semibold text-green-600">{client.score}</td>
                  <td className="py-3 px-4">
                    <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Voir Profil</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Gestion Dossiers Crédits
const GestionCreditsPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Gestion Dossiers Crédits</h2>
          <p className="text-gray-600">Validation, suivi et simulations de crédits</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Plus className="w-4 h-4" />
            <span>Nouvelle Demande</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[
          { label: 'En Attente', value: '23', color: 'orange' },
          { label: 'Approuvés', value: '156', color: 'green' },
          { label: 'Rejetés', value: '12', color: 'red' },
          { label: 'En Cours', value: '45', color: 'blue' }
        ].map((stat, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className={`text-3xl font-bold text-${stat.color}-600 mb-2`}>{stat.value}</div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Demandes Récentes</h3>
          <div className="space-y-4">
            {[
              { client: 'Ahmed Ben Ali', montant: '€50,000', type: 'Immobilier', statut: 'En Attente', score: '95/100' },
              { client: 'Fatma Trabelsi', montant: '€15,000', type: 'Personnel', statut: 'Approuvé', score: '87/100' },
              { client: 'Mohamed Gharbi', montant: '€25,000', type: 'Auto', statut: 'En Cours', score: '92/100' }
            ].map((demande, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="font-semibold text-gray-900">{demande.client}</div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    demande.statut === 'Approuvé' ? 'bg-green-100 text-green-700' :
                    demande.statut === 'En Attente' ? 'bg-orange-100 text-orange-700' :
                    'bg-blue-100 text-blue-700'
                  }`}>
                    {demande.statut}
                  </span>
                </div>
                <div className="text-sm text-gray-600 mb-2">{demande.type} • {demande.montant}</div>
                <div className="text-sm text-green-600 font-medium">Score IA: {demande.score}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Simulateur de Crédit IA</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Montant souhaité</label>
              <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="50000" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Durée (mois)</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                <option>12 mois</option>
                <option>24 mois</option>
                <option>36 mois</option>
                <option>60 mois</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type de crédit</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                <option>Personnel</option>
                <option>Immobilier</option>
                <option>Automobile</option>
                <option>Professionnel</option>
              </select>
            </div>
            <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
              Calculer avec IA
            </button>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-800 font-medium">Résultat IA:</div>
              <div className="text-lg font-bold text-blue-900">€456/mois</div>
              <div className="text-sm text-blue-700">Taux: 3.2% • Éligibilité: 95%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Dashboard Page
const DashboardPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Dashboard Exécutif</h2>
          <p className="text-gray-600">Vue d'ensemble temps réel de votre banque</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Download className="w-4 h-4" />
            <span>Exporter</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Plus className="w-4 h-4" />
            <span>Nouvelle Analyse</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[
          { label: 'Revenus Totaux', value: '€2,847,392', change: '+12.5%', icon: DollarSign, color: 'green' },
          { label: 'Clients Actifs', value: '1,247', change: '+8.2%', icon: Users, color: 'blue' },
          { label: 'Taux de Défaut', value: '2.1%', change: '-40%', icon: TrendingUp, color: 'purple' },
          { label: 'Score IA Moyen', value: '96%', change: '+2.1%', icon: Brain, color: 'orange' }
        ].map((metric, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <metric.icon className="w-6 h-6 text-blue-600" />
              </div>
              <span className="text-sm text-green-600 font-medium">{metric.change}</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
            <div className="text-sm text-gray-600">{metric.label}</div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Mensuelle</h3>
          <div className="h-64 bg-gradient-to-t from-blue-50 to-transparent rounded-lg flex items-end justify-between px-4 pb-4">
            {Array.from({ length: 12 }).map((_, i) => (
              <div
                key={i}
                className="bg-blue-500 rounded-t-sm"
                style={{ 
                  height: `${Math.random() * 80 + 20}%`,
                  width: '6%'
                }}
              />
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Transactions Récentes</h3>
          <div className="space-y-4">
            {[
              { client: 'Ahmed Ben Ali', montant: '+€3,200', type: 'Dépôt', time: '2h' },
              { client: 'Fatma Trabelsi', montant: '-€1,500', type: 'Crédit', time: '4h' },
              { client: 'Mohamed Gharbi', montant: '+€850', type: 'Virement', time: '6h' },
              { client: 'Leila Mansouri', montant: '-€2,100', type: 'Prêt', time: '8h' }
            ].map((transaction, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{transaction.client}</div>
                  <div className="text-sm text-gray-500">{transaction.type} • Il y a {transaction.time}</div>
                </div>
                <div className={`font-semibold ${
                  transaction.montant.startsWith('+') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {transaction.montant}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Suivi Rentabilité Client
const RentabiliteClientPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Suivi Rentabilité Client</h2>
        <p className="text-gray-600">Outils de suivi de la rentabilité par client et segment</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {[
          { label: 'ROI Moyen Client', value: '€2,847', change: '+15.2%', color: 'green' },
          { label: 'Marge Nette', value: '23.4%', change: '+2.1%', color: 'blue' },
          { label: 'Coût Acquisition', value: '€156', change: '-8.5%', color: 'purple' }
        ].map((metric, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
            <div className="text-sm text-gray-600 mb-2">{metric.label}</div>
            <div className="text-sm text-green-600 font-medium">{metric.change}</div>
          </div>
        ))}
      </div>

      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Rentabilité par Segment</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Segment</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Clients</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Revenus</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">ROI</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Tendance</th>
              </tr>
            </thead>
            <tbody>
              {[
                { segment: 'Premium', clients: '342', revenus: '€1,245,000', roi: '€3,640', tendance: '+12%' },
                { segment: 'Standard', clients: '785', revenus: '€892,000', roi: '€1,136', tendance: '+8%' },
                { segment: 'Nouveaux', clients: '120', revenus: '€156,000', roi: '€1,300', tendance: '+25%' }
              ].map((row, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 font-medium">{row.segment}</td>
                  <td className="py-3 px-4">{row.clients}</td>
                  <td className="py-3 px-4 font-semibold text-green-600">{row.revenus}</td>
                  <td className="py-3 px-4 font-semibold text-blue-600">{row.roi}</td>
                  <td className="py-3 px-4 text-green-600 font-medium">{row.tendance}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Performances Commerciales
const PerformancesCommercialesPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Performances Commerciales</h2>
        <p className="text-gray-600">Visualisation des performances par agence et région</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[
          { label: 'Agence Tunis Centre', value: '€456K', performance: '125%', color: 'green' },
          { label: 'Agence Sfax', value: '€342K', performance: '98%', color: 'blue' },
          { label: 'Agence Sousse', value: '€289K', performance: '87%', color: 'orange' },
          { label: 'Agence Bizerte', value: '€198K', performance: '76%', color: 'red' }
        ].map((agence, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="text-xl font-bold text-gray-900 mb-1">{agence.value}</div>
            <div className="text-sm text-gray-600 mb-2">{agence.label}</div>
            <div className={`text-sm font-medium text-${agence.color}-600`}>Performance: {agence.performance}</div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Conseillers</h3>
          <div className="space-y-4">
            {[
              { nom: 'Amira Benali', agence: 'Tunis Centre', ventes: '€125K', clients: '45' },
              { nom: 'Karim Mansouri', agence: 'Sfax', ventes: '€98K', clients: '38' },
              { nom: 'Leila Trabelsi', agence: 'Sousse', ventes: '€87K', clients: '32' }
            ].map((conseiller, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-semibold text-gray-900">{conseiller.nom}</div>
                  <div className="text-sm text-gray-600">{conseiller.agence}</div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">{conseiller.ventes}</div>
                  <div className="text-sm text-gray-600">{conseiller.clients} clients</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Objectifs vs Réalisé</h3>
          <div className="space-y-4">
            {[
              { produit: 'Crédits Immobiliers', objectif: '€2M', realise: '€2.4M', pourcentage: '120%' },
              { produit: 'Comptes Épargne', objectif: '€1.5M', realise: '€1.3M', pourcentage: '87%' },
              { produit: 'Cartes de Crédit', objectif: '€800K', realise: '€950K', pourcentage: '119%' }
            ].map((objectif, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">{objectif.produit}</span>
                  <span className="text-sm font-semibold text-blue-600">{objectif.pourcentage}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: objectif.pourcentage }}
                  ></div>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-600">
                  <span>Objectif: {objectif.objectif}</span>
                  <span>Réalisé: {objectif.realise}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Gestion Litiges
const GestionLitigesPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Gestion des Litiges</h2>
          <p className="text-gray-600">Interface de gestion des litiges et incidents client</p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
          <Plus className="w-4 h-4" />
          <span>Nouveau Litige</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[
          { label: 'Ouverts', value: '12', color: 'red' },
          { label: 'En Cours', value: '8', color: 'orange' },
          { label: 'Résolus', value: '156', color: 'green' },
          { label: 'Escaladés', value: '3', color: 'purple' }
        ].map((stat, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className={`text-3xl font-bold text-${stat.color}-600 mb-2`}>{stat.value}</div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </div>
        ))}
      </div>

      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Litiges Récents</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">ID</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Client</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Type</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Priorité</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Statut</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Assigné à</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {[
                { id: 'LIT-001', client: 'Ahmed Ben Ali', type: 'Transaction non autorisée', priorite: 'Haute', statut: 'Ouvert', assigne: 'Amira Benali' },
                { id: 'LIT-002', client: 'Fatma Trabelsi', type: 'Frais incorrects', priorite: 'Moyenne', statut: 'En Cours', assigne: 'Karim Mansouri' },
                { id: 'LIT-003', client: 'Mohamed Gharbi', type: 'Problème carte', priorite: 'Basse', statut: 'Résolu', assigne: 'Leila Trabelsi' }
              ].map((litige, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 font-medium text-blue-600">{litige.id}</td>
                  <td className="py-3 px-4">{litige.client}</td>
                  <td className="py-3 px-4">{litige.type}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      litige.priorite === 'Haute' ? 'bg-red-100 text-red-700' :
                      litige.priorite === 'Moyenne' ? 'bg-orange-100 text-orange-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {litige.priorite}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      litige.statut === 'Ouvert' ? 'bg-red-100 text-red-700' :
                      litige.statut === 'En Cours' ? 'bg-orange-100 text-orange-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {litige.statut}
                    </span>
                  </td>
                  <td className="py-3 px-4">{litige.assigne}</td>
                  <td className="py-3 px-4">
                    <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Traiter</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Formulaires Intelligents
const FormulairesIntelligentsPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Formulaires Intelligents</h2>
        <p className="text-gray-600">Saisie automatisée de données avec IA</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ouverture de Compte IA</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Scanner CIN/Passeport</label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Cliquez pour scanner ou glissez le document</p>
                <p className="text-xs text-gray-500 mt-1">IA extraira automatiquement les données</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Nom</label>
                <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-green-50" value="Ben Ali" readOnly />
                <p className="text-xs text-green-600 mt-1">✓ Extrait automatiquement</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Prénom</label>
                <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-green-50" value="Ahmed" readOnly />
                <p className="text-xs text-green-600 mt-1">✓ Extrait automatiquement</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Numéro CIN</label>
              <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-green-50" value="12345678" readOnly />
              <p className="text-xs text-green-600 mt-1">✓ Vérifié avec base BCT</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Adresse</label>
              <textarea className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-green-50" rows={3} value="Avenue Habib Bourguiba, Tunis" readOnly />
              <p className="text-xs text-green-600 mt-1">✓ Géolocalisée automatiquement</p>
            </div>

            <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
              Valider avec IA
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Vérifications IA en Temps Réel</h3>
          <div className="space-y-4">
            {[
              { check: 'Authentification biométrique', status: 'Validé', icon: '✓', color: 'green' },
              { check: 'Vérification CIN avec BCT', status: 'Validé', icon: '✓', color: 'green' },
              { check: 'Analyse anti-blanchiment', status: 'En cours', icon: '⏳', color: 'orange' },
              { check: 'Score de crédit IA', status: 'Calculé: 95/100', icon: '✓', color: 'green' },
              { check: 'Géolocalisation adresse', status: 'Confirmée', icon: '✓', color: 'green' },
              { check: 'Détection de fraude', status: 'Aucune anomalie', icon: '✓', color: 'green' }
            ].map((verification, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className={`text-${verification.color}-600 font-bold`}>{verification.icon}</span>
                  <span className="text-sm font-medium text-gray-900">{verification.check}</span>
                </div>
                <span className={`text-sm font-medium text-${verification.color}-600`}>{verification.status}</span>
              </div>
            ))}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Recommandations IA</h4>
            <ul className="space-y-1 text-sm text-blue-800">
              <li>• Client éligible pour compte Premium</li>
              <li>• Proposer carte de crédit Gold</li>
              <li>• Offrir crédit immobilier préférentiel</li>
              <li>• Assurance vie recommandée</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

// KPI Temps Réel
const KPITempsReelPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">KPI Temps Réel</h2>
        <p className="text-gray-600">Suivi des indicateurs clés en temps réel</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[
          { label: 'Dépôts Aujourd\'hui', value: '€156,789', change: '+8.2%', icon: TrendingUp },
          { label: 'Crédits Accordés', value: '€89,456', change: '+12.5%', icon: CreditCard },
          { label: 'Transferts', value: '€234,567', change: '+5.7%', icon: ArrowUpRight },
          { label: 'Croissance', value: '+15.3%', change: '+2.1%', icon: BarChart3 }
        ].map((kpi, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <kpi.icon className="w-8 h-8 text-blue-600" />
              <span className="text-sm text-green-600 font-medium">{kpi.change}</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{kpi.value}</div>
            <div className="text-sm text-gray-600">{kpi.label}</div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité en Temps Réel</h3>
          <div className="space-y-3">
            {[
              { time: '14:32', action: 'Nouveau dépôt', montant: '+€5,000', client: 'Ahmed Ben Ali' },
              { time: '14:28', action: 'Crédit approuvé', montant: '€25,000', client: 'Fatma Trabelsi' },
              { time: '14:25', action: 'Virement international', montant: '€8,500', client: 'Mohamed Gharbi' },
              { time: '14:22', action: 'Ouverture compte', montant: '€1,000', client: 'Leila Mansouri' }
            ].map((activite, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{activite.action}</div>
                  <div className="text-sm text-gray-600">{activite.client} • {activite.time}</div>
                </div>
                <div className="font-semibold text-green-600">{activite.montant}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes Système</h3>
          <div className="space-y-3">
            {[
              { type: 'success', message: 'Objectif mensuel atteint à 125%' },
              { type: 'warning', message: 'Pic de transactions détecté' },
              { type: 'info', message: 'Nouveau client Premium ajouté' },
              { type: 'success', message: 'Système IA mis à jour' }
            ].map((alerte, index) => (
              <div key={index} className={`p-3 rounded-lg ${
                alerte.type === 'success' ? 'bg-green-50 border border-green-200' :
                alerte.type === 'warning' ? 'bg-orange-50 border border-orange-200' :
                'bg-blue-50 border border-blue-200'
              }`}>
                <div className={`text-sm font-medium ${
                  alerte.type === 'success' ? 'text-green-800' :
                  alerte.type === 'warning' ? 'text-orange-800' :
                  'text-blue-800'
                }`}>
                  {alerte.message}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Authentification Biométrique
const BiometriePage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Authentification Biométrique</h2>
        <p className="text-gray-600">Sécurité avancée avec Face ID, empreintes et reconnaissance vocale</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration Biométrique</h3>
          <div className="space-y-6">
            {[
              { type: 'Face ID', icon: '👤', status: 'Activé', description: 'Reconnaissance faciale 3D' },
              { type: 'Empreinte', icon: '👆', status: 'Activé', description: 'Scanner d\'empreinte digitale' },
              { type: 'Voix', icon: '🎤', status: 'En attente', description: 'Reconnaissance vocale' },
              { type: 'Iris', icon: '👁️', status: 'Disponible', description: 'Scanner d\'iris (optionnel)' }
            ].map((bio, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">{bio.icon}</div>
                  <div>
                    <div className="font-medium text-gray-900">{bio.type}</div>
                    <div className="text-sm text-gray-600">{bio.description}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    bio.status === 'Activé' ? 'bg-green-100 text-green-700' :
                    bio.status === 'En attente' ? 'bg-orange-100 text-orange-700' :
                    'bg-blue-100 text-blue-700'
                  }`}>
                    {bio.status}
                  </span>
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    Configurer
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sécurité Avancée</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Authentification 2FA</h4>
              <div className="space-y-2">
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" checked />
                  <span className="text-sm text-blue-800">SMS OTP</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" checked />
                  <span className="text-sm text-blue-800">Email OTP</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm text-blue-800">Application Authenticator</span>
                </label>
              </div>
            </div>

            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Détection d'Anomalies</h4>
              <div className="space-y-2 text-sm text-green-800">
                <div>✓ Géolocalisation inhabituelle</div>
                <div>✓ Fréquence de connexion anormale</div>
                <div>✓ Appareil non reconnu</div>
                <div>✓ Horaires de connexion suspects</div>
              </div>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Gestion des Sessions</h4>
              <div className="space-y-2 text-sm text-purple-800">
                <div>• Expiration automatique: 30 min</div>
                <div>• Chiffrement AES-256</div>
                <div>• Tokens JWT sécurisés</div>
                <div>• Audit trail complet</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// IA Recommandations
const IARecommandationsPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">IA Recommandations</h2>
        <p className="text-gray-600">Recommandations personnalisées de produits bancaires</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommandations Actives</h3>
          <div className="space-y-4">
            {[
              { client: 'Ahmed Ben Ali', produit: 'Crédit Immobilier', score: '95%', montant: '€150K' },
              { client: 'Fatma Trabelsi', produit: 'Carte Gold', score: '87%', montant: '€5K limite' },
              { client: 'Mohamed Gharbi', produit: 'Assurance Vie', score: '92%', montant: '€200/mois' }
            ].map((rec, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="font-semibold text-gray-900 mb-1">{rec.client}</div>
                <div className="text-sm text-blue-600 mb-2">{rec.produit} • {rec.montant}</div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-green-600 font-medium">Score IA: {rec.score}</span>
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Proposer</button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Analyse Prédictive</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Prédictions 30 jours</h4>
              <div className="space-y-2 text-sm text-blue-800">
                <div>• 23 nouveaux crédits probables</div>
                <div>• €450K de dépôts attendus</div>
                <div>• 12 clients à risque de churn</div>
                <div>• 8 opportunités cross-selling</div>
              </div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Opportunités Détectées</h4>
              <div className="space-y-2 text-sm text-green-800">
                <div>• 15 clients éligibles Premium</div>
                <div>• 28 prospects investissement</div>
                <div>• 34 renouvellements carte</div>
                <div>• 19 assurances recommandées</div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance IA</h3>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">87%</div>
              <div className="text-sm text-gray-600">Taux d'acceptation</div>
            </div>
            <div className="space-y-3">
              {[
                { metric: 'Précision recommandations', value: '94%' },
                { metric: 'Conversion cross-selling', value: '23%' },
                { metric: 'Satisfaction client', value: '4.8/5' },
                { metric: 'ROI campagnes IA', value: '+156%' }
              ].map((metric, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{metric.metric}</span>
                  <span className="font-semibold text-blue-600">{metric.value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Analyse Comportementale
const AnalyseComportementalePage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Analyse Comportementale</h2>
        <p className="text-gray-600">Analyse IA du comportement client pour personnalisation</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Profils Comportementaux</h3>
          <div className="space-y-4">
            {[
              { type: 'Épargnant Prudent', clients: '342', description: 'Préfère sécurité et épargne', color: 'green' },
              { type: 'Investisseur Actif', clients: '156', description: 'Recherche rendement élevé', color: 'blue' },
              { type: 'Consommateur Digital', clients: '289', description: 'Utilise services en ligne', color: 'purple' },
              { type: 'Traditionnel', clients: '460', description: 'Préfère agences physiques', color: 'orange' }
            ].map((profil, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="font-semibold text-gray-900">{profil.type}</div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium bg-${profil.color}-100 text-${profil.color}-700`}>
                    {profil.clients} clients
                  </span>
                </div>
                <div className="text-sm text-gray-600">{profil.description}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes Comportementales</h3>
          <div className="space-y-4">
            {[
              { client: 'Ahmed Ben Ali', alerte: 'Changement pattern dépenses', risque: 'Moyen' },
              { client: 'Fatma Trabelsi', alerte: 'Baisse activité compte', risque: 'Élevé' },
              { client: 'Mohamed Gharbi', alerte: 'Nouveau comportement épargne', risque: 'Faible' }
            ].map((alerte, index) => (
              <div key={index} className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="font-semibold text-orange-900 mb-1">{alerte.client}</div>
                <div className="text-sm text-orange-800 mb-2">{alerte.alerte}</div>
                <div className="flex items-center justify-between">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    alerte.risque === 'Élevé' ? 'bg-red-100 text-red-700' :
                    alerte.risque === 'Moyen' ? 'bg-orange-100 text-orange-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    Risque: {alerte.risque}
                  </span>
                  <button className="text-orange-700 hover:text-orange-800 text-sm font-medium">Analyser</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Détection Fraude
const DetectionFraudePage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Détection Fraude IA</h2>
        <p className="text-gray-600">Système automatique de détection et prévention des fraudes</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[
          { label: 'Transactions Bloquées', value: '12', color: 'red' },
          { label: 'Alertes Actives', value: '8', color: 'orange' },
          { label: 'Faux Positifs', value: '0.1%', color: 'green' },
          { label: 'Économies', value: '€45K', color: 'blue' }
        ].map((stat, index) => (
          <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className={`text-3xl font-bold text-${stat.color}-600 mb-2`}>{stat.value}</div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </div>
        ))}
      </div>

      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes Fraude Récentes</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Heure</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Client</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Transaction</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Risque IA</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Statut</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-900">Action</th>
              </tr>
            </thead>
            <tbody>
              {[
                { heure: '14:32', client: 'Ahmed Ben Ali', transaction: 'Virement €15,000', risque: '95%', statut: 'Bloqué' },
                { heure: '14:28', client: 'Fatma Trabelsi', transaction: 'Achat en ligne €2,500', risque: '78%', statut: 'En Analyse' },
                { heure: '14:25', client: 'Mohamed Gharbi', transaction: 'Retrait ATM €500', risque: '45%', statut: 'Autorisé' }
              ].map((alerte, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">{alerte.heure}</td>
                  <td className="py-3 px-4">{alerte.client}</td>
                  <td className="py-3 px-4">{alerte.transaction}</td>
                  <td className="py-3 px-4">
                    <span className={`font-semibold ${
                      parseInt(alerte.risque) > 80 ? 'text-red-600' :
                      parseInt(alerte.risque) > 60 ? 'text-orange-600' :
                      'text-green-600'
                    }`}>
                      {alerte.risque}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      alerte.statut === 'Bloqué' ? 'bg-red-100 text-red-700' :
                      alerte.statut === 'En Analyse' ? 'bg-orange-100 text-orange-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {alerte.statut}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Examiner</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Rapports BI Dynamiques
const RapportsBIPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Rapports BI Dynamiques</h2>
          <p className="text-gray-600">Générateur de rapports intelligents sur revenus, dettes et tendances</p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
          <Plus className="w-4 h-4" />
          <span>Nouveau Rapport</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Rapports Prédéfinis</h3>
          <div className="space-y-3">
            {[
              { nom: 'Performance Mensuelle', type: 'PDF', taille: '2.3 MB' },
              { nom: 'Analyse Risques', type: 'Excel', taille: '1.8 MB' },
              { nom: 'Tendances Clients', type: 'PDF', taille: '3.1 MB' },
              { nom: 'ROI Produits', type: 'PowerBI', taille: '5.2 MB' }
            ].map((rapport, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{rapport.nom}</div>
                  <div className="text-sm text-gray-600">{rapport.type} • {rapport.taille}</div>
                </div>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Télécharger</button>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Générateur IA</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type de rapport</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                <option>Analyse Financière</option>
                <option>Performance Commerciale</option>
                <option>Gestion des Risques</option>
                <option>Satisfaction Client</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Période</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                <option>Derniers 30 jours</option>
                <option>Dernier trimestre</option>
                <option>Dernière année</option>
                <option>Personnalisé</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
              <div className="grid grid-cols-3 gap-2">
                <button className="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">PDF</button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">Excel</button>
                <button className="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">PowerBI</button>
              </div>
            </div>
            <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
              Générer avec IA
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Insights IA</h3>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Tendances Détectées</h4>
              <div className="space-y-2 text-sm text-blue-800">
                <div>• Croissance dépôts +15% ce mois</div>
                <div>• Pic demandes crédit immobilier</div>
                <div>• Baisse utilisation cartes crédit</div>
                <div>• Augmentation clients digitaux</div>
              </div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Recommandations</h4>
              <div className="space-y-2 text-sm text-green-800">
                <div>• Lancer campagne cartes crédit</div>
                <div>• Optimiser taux immobilier</div>
                <div>• Développer services digitaux</div>
                <div>• Cibler segment jeunes</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Technologies des Grandes Entreprises
const TechnologiesEntreprisesPage: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Technologies des Plus Grandes Entreprises Mondiales</h2>
        <p className="text-gray-600">Technologies révolutionnaires utilisées par Apple, Google, Microsoft, Amazon, Tesla, Meta, NVIDIA</p>
      </div>

      {/* GAFAM Technologies */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white mb-8">
        <h3 className="text-2xl font-bold mb-6">🏢 Technologies GAFAM (Google, Apple, Facebook, Amazon, Microsoft)</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              company: 'Google/Alphabet',
              logo: '🔍',
              technologies: [
                { name: 'TPU v5 Pods', desc: '1.1 ExaFLOPS pour ML', usage: 'Modèles IA bancaires' },
                { name: 'Quantum AI Sycamore', desc: '70 qubits superconducteurs', usage: 'Cryptographie quantique' },
                { name: 'LaMDA/Bard AI', desc: 'LLM conversationnel', usage: 'Assistant bancaire IA' },
                { name: 'Tensor Processing Units', desc: 'Puces IA spécialisées', usage: 'Inférence temps réel' }
              ]
            },
            {
              company: 'Apple Inc.',
              logo: '🍎',
              technologies: [
                { name: 'M3 Ultra Neural Engine', desc: '15.8 TOPS on-device', usage: 'IA mobile sécurisée' },
                { name: 'Secure Enclave T2', desc: 'Cryptage niveau militaire', usage: 'Authentification biométrique' },
                { name: 'Face ID TrueDepth', desc: 'Reconnaissance faciale 3D', usage: 'Connexion sécurisée' },
                { name: 'Apple Silicon Custom', desc: 'Puces ARM optimisées', usage: 'Performance énergétique' }
              ]
            },
            {
              company: 'Meta Platforms',
              logo: '📘',
              technologies: [
                { name: 'Reality Labs VR/AR', desc: 'Metaverse immersif', usage: 'Banking en réalité virtuelle' },
                { name: 'PyTorch Framework', desc: 'Deep learning open-source', usage: 'Développement modèles IA' },
                { name: 'Codec Avatars', desc: 'Avatars photorealistic', usage: 'Conseillers virtuels' },
                { name: 'Horizon Workrooms', desc: 'Collaboration VR', usage: 'Réunions bancaires virtuelles' }
              ]
            },
            {
              company: 'Amazon Web Services',
              logo: '📦',
              technologies: [
                { name: 'Graviton4 Processors', desc: '64-core ARM custom', usage: 'Infrastructure cloud bancaire' },
                { name: 'Trainium2 AI Chips', desc: 'Training IA optimisé', usage: 'Entraînement modèles financiers' },
                { name: 'Bedrock Foundation Models', desc: 'LLM as a Service', usage: 'IA générative bancaire' },
                { name: 'Quantum Computing Center', desc: 'Accès ordinateurs quantiques', usage: 'Recherche financière' }
              ]
            },
            {
              company: 'Microsoft Corp.',
              logo: '🪟',
              technologies: [
                { name: 'Azure OpenAI Service', desc: 'GPT-4 enterprise', usage: 'Assistant IA conversationnel' },
                { name: 'HoloLens 3 Mixed Reality', desc: 'Réalité augmentée 8K', usage: 'Visualisation données 3D' },
                { name: 'Azure Quantum Cloud', desc: 'Computing quantique hybride', usage: 'Optimisation portefeuille' },
                { name: 'Copilot AI Assistant', desc: 'IA intégrée Office 365', usage: 'Productivité bancaire' }
              ]
            },
            {
              company: 'Tesla Inc.',
              logo: '⚡',
              technologies: [
                { name: 'Dojo v2 Supercomputer', desc: 'Neural network training', usage: 'Prédictions marché' },
                { name: 'FSD Computer v4', desc: '144 TOPS décisions temps réel', usage: 'Trading haute fréquence' },
                { name: 'Neural Networks', desc: 'Réseaux de neurones custom', usage: 'Analyse comportementale' },
                { name: 'Autopilot AI', desc: 'IA de conduite autonome', usage: 'Décisions automatisées' }
              ]
            }
          ].map((company, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-xl rounded-xl p-6 border border-white/20">
              <div className="flex items-center space-x-3 mb-4">
                <div className="text-3xl">{company.logo}</div>
                <div>
                  <h4 className="font-bold text-white text-lg">{company.company}</h4>
                  <p className="text-purple-200 text-sm">Fortune 500 Tech Giant</p>
                </div>
              </div>

              <div className="space-y-3">
                {company.technologies.map((tech, idx) => (
                  <div key={idx} className="bg-white/5 rounded-lg p-3">
                    <div className="font-semibold text-white text-sm mb-1">{tech.name}</div>
                    <div className="text-purple-200 text-xs mb-2">{tech.desc}</div>
                    <div className="text-cyan-300 text-xs">Usage: {tech.usage}</div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Technologies Semiconducteurs */}
      <div className="bg-gradient-to-r from-green-600 via-teal-600 to-blue-600 rounded-2xl p-8 text-white mb-8">
        <h3 className="text-2xl font-bold mb-6">🔬 Technologies Semiconducteurs de Pointe</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              company: 'NVIDIA Corp.',
              chip: 'H100 Hopper',
              specs: ['80GB HBM3', '3TB/s bandwidth', '4000 CUDA cores', 'Transformer Engine'],
              usage: 'LLM financiers et trading IA'
            },
            {
              company: 'Intel Corp.',
              chip: 'Ponte Vecchio',
              specs: ['47 tiles', 'Xe-HPC arch', '128GB HBM2e', 'Exascale ready'],
              usage: 'Simulations Monte Carlo risques'
            },
            {
              company: 'AMD Inc.',
              chip: 'MI300X',
              specs: ['192GB HBM3', '5.3TB/s bandwidth', 'CDNA3 arch', 'AI inference'],
              usage: 'Inférence modèles IA massifs'
            },
            {
              company: 'Qualcomm Inc.',
              chip: 'Snapdragon X Elite',
              specs: ['45 TOPS NPU', 'Oryon CPU', '5G modem', 'Always-on AI'],
              usage: 'Banking mobile intelligent'
            },
            {
              company: 'Broadcom Inc.',
              chip: 'Jericho3-AI',
              specs: ['25.6Tbps switching', 'AI acceleration', 'Low latency', 'Programmable'],
              usage: 'Infrastructure réseau bancaire'
            },
            {
              company: 'Marvell Technology',
              chip: 'OCTEON 10',
              specs: ['ARM Neoverse', 'Hardware security', '5G ready', 'Edge computing'],
              usage: 'Sécurité edge banking'
            },
            {
              company: 'Cerebras Systems',
              chip: 'WSE-3 Wafer',
              specs: ['4 trillion transistors', '900,000 cores', '44GB on-chip', 'Largest chip'],
              usage: 'Training IA financière massive'
            },
            {
              company: 'Graphcore Ltd.',
              chip: 'Bow IPU',
              specs: ['1472 cores', 'MIMD architecture', 'Ultra-low latency', 'Sparse compute'],
              usage: 'Inférence temps réel trading'
            }
          ].map((tech, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="mb-3">
                <h4 className="font-bold text-white text-sm">{tech.company}</h4>
                <p className="text-green-200 text-lg font-semibold">{tech.chip}</p>
              </div>

              <div className="mb-3">
                <h5 className="text-white font-semibold text-xs mb-2">Spécifications:</h5>
                <div className="space-y-1">
                  {tech.specs.map((spec, idx) => (
                    <div key={idx} className="text-green-200 text-xs flex items-center">
                      <div className="w-1 h-1 bg-green-300 rounded-full mr-2"></div>
                      {spec}
                    </div>
                  ))}
                </div>
              </div>

              <div className="text-cyan-300 text-xs">
                <strong>Banking:</strong> {tech.usage}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Technologies Émergentes */}
      <div className="bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 rounded-2xl p-8 text-white">
        <h3 className="text-2xl font-bold mb-6">🚀 Technologies Émergentes - Niveau Science-Fiction</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              name: 'Quantum Supremacy Computing',
              company: 'IBM/Google/IonQ',
              description: 'Ordinateurs quantiques avec correction d\'erreur pour calculs impossibles classiquement',
              applications: ['Cryptographie post-quantique', 'Optimisation portefeuille', 'Simulation risques'],
              status: 'Recherche Avancée',
              timeline: '2025-2030'
            },
            {
              name: 'Neuromorphic Computing',
              company: 'Intel/IBM/BrainChip',
              description: 'Puces imitant le cerveau humain pour IA ultra-efficace et apprentissage continu',
              applications: ['IA edge ultra-efficace', 'Reconnaissance patterns', 'Adaptation temps réel'],
              status: 'Prototypes',
              timeline: '2024-2027'
            },
            {
              name: 'Photonic Computing',
              company: 'Xanadu/PsiQuantum',
              description: 'Calculs à la vitesse de la lumière pour parallélisme massif',
              applications: ['Trading ultra-haute fréquence', 'Calculs massivement parallèles', 'Efficacité énergétique'],
              status: 'Développement',
              timeline: '2026-2030'
            },
            {
              name: 'DNA Data Storage',
              company: 'Microsoft/Twist Bio',
              description: 'Stockage de données dans l\'ADN synthétique pour archivage millénaire',
              applications: ['Archives bancaires permanentes', 'Compliance long terme', 'Sécurité biologique'],
              status: 'Laboratoire',
              timeline: '2028-2035'
            },
            {
              name: 'Brain-Computer Interface',
              company: 'Neuralink/Kernel',
              description: 'Interface directe cerveau-ordinateur pour contrôle mental des systèmes',
              applications: ['Trading par la pensée', 'Authentification mentale', 'Intuition augmentée'],
              status: 'Expérimental',
              timeline: '2030-2040'
            },
            {
              name: 'Molecular Computing',
              company: 'Harvard/MIT Labs',
              description: 'Calculs au niveau moléculaire avec auto-assemblage et bio-compatibilité',
              applications: ['Computing bio-intégré', 'Capteurs moléculaires', 'Auto-réparation'],
              status: 'Recherche Fondamentale',
              timeline: '2035-2050'
            }
          ].map((tech, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-xl rounded-xl p-6 border border-white/20">
              <div className="mb-4">
                <h4 className="font-bold text-white text-lg mb-1">{tech.name}</h4>
                <p className="text-orange-200 text-sm">{tech.company}</p>
              </div>

              <p className="text-orange-100 text-sm mb-4">{tech.description}</p>

              <div className="mb-4">
                <h5 className="text-white font-semibold text-sm mb-2">Applications Bancaires:</h5>
                <ul className="space-y-1">
                  {tech.applications.map((app, idx) => (
                    <li key={idx} className="text-orange-200 text-xs flex items-center">
                      <div className="w-1 h-1 bg-orange-300 rounded-full mr-2"></div>
                      {app}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex items-center justify-between">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  tech.status === 'Recherche Avancée' ? 'bg-blue-400/20 text-blue-300' :
                  tech.status === 'Prototypes' ? 'bg-green-400/20 text-green-300' :
                  tech.status === 'Développement' ? 'bg-yellow-400/20 text-yellow-300' :
                  tech.status === 'Laboratoire' ? 'bg-purple-400/20 text-purple-300' :
                  tech.status === 'Expérimental' ? 'bg-red-400/20 text-red-300' :
                  'bg-pink-400/20 text-pink-300'
                }`}>
                  {tech.status}
                </span>
                <span className="text-xs text-orange-300">{tech.timeline}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// App principale avec navigation
const FixedBankingMainApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState('accueil');
  const [authMode, setAuthMode] = useState<'biometric' | 'traditional'>('biometric');
  const { user, logout, login } = useAuth();

  const handleBiometricSuccess = (userData: any) => {
    login(userData);
  };

  const handleBiometricFailed = () => {
    setAuthMode('traditional');
  };

  if (!user) {
    if (authMode === 'biometric') {
      return (
        <BiometricAuth
          onAuthSuccess={handleBiometricSuccess}
          onAuthFailed={handleBiometricFailed}
        />
      );
    } else {
      return (
        <div>
          <div className="fixed top-4 right-4 z-50">
            <button
              onClick={() => setAuthMode('biometric')}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Camera className="w-4 h-4" />
              <span>Authentification Biométrique</span>
            </button>
          </div>
          <UltraModernLogin />
        </div>
      );
    }
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'accueil':
        return <PremiumAccueilPage />;
      case 'dashboard':
        return <DashboardPage />;
      case 'clients':
        return <CRMBancairePage />;
      case 'credits':
        return <GestionCreditsPage />;
      case 'rentabilite':
        return <RentabiliteClientPage />;
      case 'performances':
        return <PerformancesCommercialesPage />;
      case 'litiges':
        return <GestionLitigesPage />;
      case 'formulaires':
        return <FormulairesIntelligentsPage />;
      case 'kpi':
        return <KPITempsReelPage />;
      case 'biometrie':
        return <BiometriePage />;
      case 'ia-recommandations':
        return <IARecommandationsPage />;
      case 'analyse-comportementale':
        return <AnalyseComportementalePage />;
      case 'detection-fraude':
        return <DetectionFraudePage />;
      case 'rapports-bi':
        return <RapportsBIPage />;
      case 'tech-entreprises':
        return <TechnologiesEntreprisesPage />;
      default:
        return (
          <div className="p-6">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">{activeTab}</h2>
            <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-200">
              <p className="text-gray-600">Module en cours de développement...</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-slate-100 flex">
      <PremiumBankingSidebar activeTab={activeTab} onTabChange={setActiveTab} />
      <div className="flex-1 flex flex-col">
        <PremiumBankingHeader user={user} onLogout={logout} />
        <main className="flex-1">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

// App avec AuthProvider
const FixedBankingApp: React.FC = () => {
  return (
    <AuthProvider>
      <FixedBankingMainApp />
    </AuthProvider>
  );
};

export default FixedBankingApp;
