import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Composant de login minimal
const MinimalLogin: React.FC = () => {
  const { login, isAuthenticated, user } = useAuth();
  const [username, setUsername] = useState('directeur.credit');
  const [pinCode, setPinCode] = useState('1234');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login(username, pinCode);
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  if (isAuthenticated) {
    return (
      <div style={{ padding: '20px', backgroundColor: '#f0f9ff', minHeight: '100vh' }}>
        <h1>🎉 TuniBankX - Connexion Réussie!</h1>
        <p>Bienvenue, {user?.prenom} {user?.nom}</p>
        <p>Rôle: {user?.role}</p>
        <div style={{ marginTop: '20px', padding: '20px', backgroundColor: 'white', borderRadius: '8px' }}>
          <h2>🏦 Dashboard Bancaire</h2>
          <p>✅ Système opérationnel</p>
          <p>✅ Authentification réussie</p>
          <p>✅ Base de données connectée</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', backgroundColor: '#dbeafe', minHeight: '100vh' }}>
      <div style={{ maxWidth: '400px', margin: '0 auto', backgroundColor: 'white', padding: '30px', borderRadius: '10px', boxShadow: '0 4px 6px rgba(0,0,0,0.1)' }}>
        <h1 style={{ color: '#1e40af', marginBottom: '20px' }}>🏦 TuniBankX</h1>
        <h2 style={{ color: '#374151', marginBottom: '20px' }}>Connexion Sécurisée</h2>
        
        <form onSubmit={handleLogin}>
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Nom d'utilisateur:
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              style={{ width: '100%', padding: '10px', border: '2px solid #e5e7eb', borderRadius: '6px' }}
            />
          </div>
          
          <div style={{ marginBottom: '20px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
              Code PIN:
            </label>
            <input
              type="password"
              value={pinCode}
              onChange={(e) => setPinCode(e.target.value)}
              style={{ width: '100%', padding: '10px', border: '2px solid #e5e7eb', borderRadius: '6px' }}
            />
          </div>
          
          <button
            type="submit"
            style={{ 
              width: '100%', 
              padding: '12px', 
              backgroundColor: '#2563eb', 
              color: 'white', 
              border: 'none', 
              borderRadius: '6px', 
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            Se connecter
          </button>
        </form>
        
        <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f3f4f6', borderRadius: '6px' }}>
          <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
            <strong>Compte de test:</strong><br />
            Utilisateur: directeur.credit<br />
            PIN: 1234
          </p>
        </div>
      </div>
    </div>
  );
};

// App minimale
const MinimalApp: React.FC = () => {
  return (
    <AuthProvider>
      <MinimalLogin />
    </AuthProvider>
  );
};

export default MinimalApp;
