// Service API Banking TuniBankX - Interface REST Complète
// API révolutionnaire pour l'intégration avec tous les systèmes bancaires

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
  requestId: string;
  version: string;
}

export interface ApiCredentials {
  apiKey: string;
  secretKey: string;
  clientId: string;
  environment: 'sandbox' | 'production';
}

export interface AccountInfo {
  accountId: string;
  accountNumber: string;
  accountType: 'checking' | 'savings' | 'credit' | 'loan';
  balance: number;
  availableBalance: number;
  currency: string;
  status: 'active' | 'inactive' | 'blocked' | 'closed';
  openDate: string;
  lastTransactionDate: string;
  interestRate?: number;
  overdraftLimit?: number;
  minimumBalance?: number;
}

export interface TransactionRequest {
  fromAccount: string;
  toAccount: string;
  amount: number;
  currency: string;
  description: string;
  reference?: string;
  scheduledDate?: string;
  metadata?: { [key: string]: any };
}

export interface TransactionResponse {
  transactionId: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  fromAccount: string;
  toAccount: string;
  amount: number;
  currency: string;
  fees: number;
  exchangeRate?: number;
  description: string;
  reference: string;
  executionDate: string;
  valueDate: string;
  balanceAfter: number;
}

export interface CreditApplicationRequest {
  clientId: string;
  creditType: 'personal' | 'mortgage' | 'auto' | 'business' | 'revolving';
  requestedAmount: number;
  currency: string;
  term: number; // en mois
  purpose: string;
  collateral?: {
    type: string;
    value: number;
    description: string;
  };
  income: {
    monthly: number;
    source: string;
    verified: boolean;
  };
  employment: {
    employer: string;
    position: string;
    tenure: number; // en mois
    type: 'permanent' | 'temporary' | 'self_employed';
  };
}

export interface CreditApplicationResponse {
  applicationId: string;
  status: 'submitted' | 'under_review' | 'approved' | 'rejected' | 'pending_documents';
  creditScore: number;
  approvedAmount?: number;
  interestRate?: number;
  term?: number;
  monthlyPayment?: number;
  conditions?: string[];
  requiredDocuments?: string[];
  expiryDate?: string;
  rejectionReason?: string;
}

export interface PaymentRequest {
  paymentType: 'bill' | 'merchant' | 'p2p' | 'government' | 'utility';
  payeeId: string;
  payeeName: string;
  amount: number;
  currency: string;
  dueDate?: string;
  billReference?: string;
  description: string;
  fromAccount: string;
}

export interface CardRequest {
  cardType: 'debit' | 'credit' | 'prepaid';
  accountId: string;
  deliveryAddress: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  limits: {
    daily: number;
    monthly: number;
    perTransaction: number;
  };
  features: string[];
}

export class TuniBankXAPI {
  private static instance: TuniBankXAPI;
  private baseUrl: string;
  private credentials: ApiCredentials | null = null;
  private rateLimiter: Map<string, number[]> = new Map();

  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://api.tunibank.tn/v1' 
      : 'https://sandbox-api.tunibank.tn/v1';
  }

  static getInstance(): TuniBankXAPI {
    if (!TuniBankXAPI.instance) {
      TuniBankXAPI.instance = new TuniBankXAPI();
    }
    return TuniBankXAPI.instance;
  }

  // Authentification
  public authenticate(credentials: ApiCredentials): Promise<ApiResponse<{ token: string; expiresIn: number }>> {
    this.credentials = credentials;
    
    return this.makeRequest('POST', '/auth/token', {
      apiKey: credentials.apiKey,
      secretKey: credentials.secretKey,
      clientId: credentials.clientId
    });
  }

  // Gestion des comptes
  public async getAccounts(clientId: string): Promise<ApiResponse<AccountInfo[]>> {
    return this.makeRequest('GET', `/accounts?clientId=${clientId}`);
  }

  public async getAccountDetails(accountId: string): Promise<ApiResponse<AccountInfo>> {
    return this.makeRequest('GET', `/accounts/${accountId}`);
  }

  public async getAccountBalance(accountId: string): Promise<ApiResponse<{ balance: number; availableBalance: number; currency: string }>> {
    return this.makeRequest('GET', `/accounts/${accountId}/balance`);
  }

  public async getAccountTransactions(
    accountId: string, 
    options?: {
      startDate?: string;
      endDate?: string;
      limit?: number;
      offset?: number;
      type?: string;
    }
  ): Promise<ApiResponse<TransactionResponse[]>> {
    const params = new URLSearchParams();
    if (options?.startDate) params.append('startDate', options.startDate);
    if (options?.endDate) params.append('endDate', options.endDate);
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.type) params.append('type', options.type);

    return this.makeRequest('GET', `/accounts/${accountId}/transactions?${params.toString()}`);
  }

  // Gestion des transactions
  public async createTransfer(request: TransactionRequest): Promise<ApiResponse<TransactionResponse>> {
    // Validation des données
    if (!this.validateTransactionRequest(request)) {
      throw new Error('Données de transaction invalides');
    }

    return this.makeRequest('POST', '/transactions/transfer', request);
  }

  public async getTransactionStatus(transactionId: string): Promise<ApiResponse<TransactionResponse>> {
    return this.makeRequest('GET', `/transactions/${transactionId}`);
  }

  public async cancelTransaction(transactionId: string, reason: string): Promise<ApiResponse<{ cancelled: boolean }>> {
    return this.makeRequest('POST', `/transactions/${transactionId}/cancel`, { reason });
  }

  // Gestion des paiements
  public async createPayment(request: PaymentRequest): Promise<ApiResponse<TransactionResponse>> {
    return this.makeRequest('POST', '/payments', request);
  }

  public async getPaymentHistory(
    accountId: string,
    options?: { startDate?: string; endDate?: string; payeeId?: string }
  ): Promise<ApiResponse<TransactionResponse[]>> {
    const params = new URLSearchParams();
    params.append('accountId', accountId);
    if (options?.startDate) params.append('startDate', options.startDate);
    if (options?.endDate) params.append('endDate', options.endDate);
    if (options?.payeeId) params.append('payeeId', options.payeeId);

    return this.makeRequest('GET', `/payments/history?${params.toString()}`);
  }

  // Gestion des crédits
  public async submitCreditApplication(request: CreditApplicationRequest): Promise<ApiResponse<CreditApplicationResponse>> {
    return this.makeRequest('POST', '/credits/applications', request);
  }

  public async getCreditApplicationStatus(applicationId: string): Promise<ApiResponse<CreditApplicationResponse>> {
    return this.makeRequest('GET', `/credits/applications/${applicationId}`);
  }

  public async getCreditOffers(clientId: string): Promise<ApiResponse<any[]>> {
    return this.makeRequest('GET', `/credits/offers?clientId=${clientId}`);
  }

  public async calculateLoanPayment(
    amount: number, 
    interestRate: number, 
    term: number
  ): Promise<ApiResponse<{ monthlyPayment: number; totalInterest: number; totalAmount: number }>> {
    return this.makeRequest('POST', '/credits/calculate', {
      amount,
      interestRate,
      term
    });
  }

  // Gestion des cartes
  public async requestCard(request: CardRequest): Promise<ApiResponse<{ cardId: string; estimatedDelivery: string }>> {
    return this.makeRequest('POST', '/cards/request', request);
  }

  public async getCardDetails(cardId: string): Promise<ApiResponse<any>> {
    return this.makeRequest('GET', `/cards/${cardId}`);
  }

  public async blockCard(cardId: string, reason: string): Promise<ApiResponse<{ blocked: boolean }>> {
    return this.makeRequest('POST', `/cards/${cardId}/block`, { reason });
  }

  public async unblockCard(cardId: string): Promise<ApiResponse<{ unblocked: boolean }>> {
    return this.makeRequest('POST', `/cards/${cardId}/unblock`);
  }

  // Services d'analyse et reporting
  public async getAccountAnalytics(
    accountId: string,
    period: 'week' | 'month' | 'quarter' | 'year'
  ): Promise<ApiResponse<any>> {
    return this.makeRequest('GET', `/analytics/accounts/${accountId}?period=${period}`);
  }

  public async getSpendingCategories(
    accountId: string,
    startDate: string,
    endDate: string
  ): Promise<ApiResponse<any[]>> {
    return this.makeRequest('GET', `/analytics/spending/${accountId}?startDate=${startDate}&endDate=${endDate}`);
  }

  public async generateStatement(
    accountId: string,
    startDate: string,
    endDate: string,
    format: 'pdf' | 'csv' | 'json'
  ): Promise<ApiResponse<{ downloadUrl: string; expiresAt: string }>> {
    return this.makeRequest('POST', '/statements/generate', {
      accountId,
      startDate,
      endDate,
      format
    });
  }

  // Services de notification
  public async subscribeToNotifications(
    accountId: string,
    webhookUrl: string,
    events: string[]
  ): Promise<ApiResponse<{ subscriptionId: string }>> {
    return this.makeRequest('POST', '/notifications/subscribe', {
      accountId,
      webhookUrl,
      events
    });
  }

  public async unsubscribeFromNotifications(subscriptionId: string): Promise<ApiResponse<{ unsubscribed: boolean }>> {
    return this.makeRequest('DELETE', `/notifications/subscriptions/${subscriptionId}`);
  }

  // Services de conformité et KYC
  public async performKYCCheck(clientId: string, documents: any[]): Promise<ApiResponse<any>> {
    return this.makeRequest('POST', '/compliance/kyc', {
      clientId,
      documents
    });
  }

  public async checkSanctions(clientName: string, clientId: string): Promise<ApiResponse<any>> {
    return this.makeRequest('POST', '/compliance/sanctions', {
      clientName,
      clientId
    });
  }

  public async reportSuspiciousActivity(
    accountId: string,
    transactionId: string,
    reason: string,
    details: any
  ): Promise<ApiResponse<{ reportId: string }>> {
    return this.makeRequest('POST', '/compliance/suspicious-activity', {
      accountId,
      transactionId,
      reason,
      details
    });
  }

  // Méthodes utilitaires privées
  private async makeRequest<T>(method: string, endpoint: string, data?: any): Promise<ApiResponse<T>> {
    // Vérification du rate limiting
    if (!this.checkRateLimit(endpoint)) {
      throw new Error('Rate limit exceeded');
    }

    const requestId = this.generateRequestId();
    const url = `${this.baseUrl}${endpoint}`;

    try {
      const headers: { [key: string]: string } = {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
        'X-API-Version': '1.0',
        'User-Agent': 'TuniBankX-SDK/1.0'
      };

      if (this.credentials) {
        headers['Authorization'] = `Bearer ${this.credentials.apiKey}`;
        headers['X-Client-ID'] = this.credentials.clientId;
      }

      const config: RequestInit = {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined
      };

      const response = await fetch(url, config);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }

      return {
        success: true,
        data: result.data,
        timestamp: Date.now(),
        requestId,
        version: '1.0'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
        requestId,
        version: '1.0'
      };
    }
  }

  private validateTransactionRequest(request: TransactionRequest): boolean {
    if (!request.fromAccount || !request.toAccount || !request.amount) {
      return false;
    }

    if (request.amount <= 0) {
      return false;
    }

    if (request.fromAccount === request.toAccount) {
      return false;
    }

    if (!['TND', 'EUR', 'USD'].includes(request.currency)) {
      return false;
    }

    return true;
  }

  private checkRateLimit(endpoint: string): boolean {
    const now = Date.now();
    const windowMs = 60000; // 1 minute
    const maxRequests = 100; // 100 requêtes par minute

    if (!this.rateLimiter.has(endpoint)) {
      this.rateLimiter.set(endpoint, []);
    }

    const requests = this.rateLimiter.get(endpoint)!;
    
    // Nettoyer les anciennes requêtes
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.rateLimiter.set(endpoint, validRequests);
    
    return true;
  }

  private generateRequestId(): string {
    return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Méthodes publiques pour la configuration
  public setEnvironment(environment: 'sandbox' | 'production'): void {
    this.baseUrl = environment === 'production' 
      ? 'https://api.tunibank.tn/v1' 
      : 'https://sandbox-api.tunibank.tn/v1';
  }

  public getApiDocumentation(): any {
    return {
      version: '1.0',
      baseUrl: this.baseUrl,
      authentication: 'API Key + Secret',
      rateLimit: '100 requests per minute',
      endpoints: {
        accounts: '/accounts',
        transactions: '/transactions',
        payments: '/payments',
        credits: '/credits',
        cards: '/cards',
        analytics: '/analytics',
        notifications: '/notifications',
        compliance: '/compliance'
      },
      sdks: {
        javascript: 'npm install tunibank-sdk',
        python: 'pip install tunibank-sdk',
        php: 'composer require tunibank/sdk',
        java: 'Maven: com.tunibank:sdk',
        csharp: 'NuGet: TuniBank.SDK'
      }
    };
  }
}
