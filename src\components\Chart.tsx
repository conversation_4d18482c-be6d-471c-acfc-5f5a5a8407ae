import React from 'react';

interface ChartProps {
  type: 'line' | 'bar' | 'doughnut';
}

const Chart: React.FC<ChartProps> = ({ type }) => {
  if (type === 'line') {
    return (
      <div className="h-64 flex items-end justify-between space-x-2">
        {[65, 78, 82, 68, 92, 85, 76, 89, 94, 87, 91, 96].map((height, index) => (
          <div key={index} className="flex-1 flex flex-col items-center">
            <div 
              className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all hover:from-blue-600 hover:to-blue-500"
              style={{ height: `${height}%` }}
            />
            <span className="text-xs text-gray-500 mt-2">
              {['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'][index]}
            </span>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'doughnut') {
    const data = [
      { label: 'Faible Risque', value: 45, color: 'text-green-500' },
      { label: 'Risque Modéré', value: 35, color: 'text-yellow-500' },
      { label: 'Risque Élevé', value: 20, color: 'text-red-500' }
    ];

    return (
      <div className="flex items-center justify-center h-64">
        <div className="relative">
          {/* Simplified doughnut representation */}
          <div className="w-32 h-32 rounded-full bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 flex items-center justify-center">
            <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
              <span className="text-lg font-bold text-gray-700">100%</span>
            </div>
          </div>
          
          {/* Legend */}
          <div className="absolute left-40 top-0 space-y-2">
            {data.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${item.color.replace('text-', 'bg-')}`} />
                <span className="text-sm text-gray-600">{item.label}: {item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center text-gray-500">Graphique {type}</div>;
};

export default Chart;