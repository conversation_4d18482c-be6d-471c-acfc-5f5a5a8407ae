import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import UltraModernLogin from './components/UltraModernLogin';
import EnterpriseHeader from './components/EnterpriseHeader';
import EnterpriseSidebar from './components/EnterpriseSidebar';
import BankingDashboard from './components/BankingDashboard';
import AdvancedClientManagement from './components/AdvancedClientManagement';
import CreditManagement from './components/CreditManagement';
import TechnologyShowcase from './components/TechnologyShowcase';
import AdvancedChatBot from './components/AdvancedChatBot';

// Version progressive de l'App pour identifier les erreurs
const ProgressiveMainApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { user, logout } = useAuth();

  if (!user) {
    return <UltraModernLogin />;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <BankingDashboard />;
      case 'clients':
        return <AdvancedClientManagement />;
      case 'credits':
        return <CreditManagement />;
      case 'analytics':
        return <TechnologyShowcase />;
      case 'documents':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Génération de Documents</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Module de génération de documents IA en développement...</p>
            </div>
          </div>
        );
      case 'reports':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Rapports et Analytics</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Module de rapports avancés en développement...</p>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Paramètres</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <p className="text-gray-600">Paramètres de l'application...</p>
            </div>
          </div>
        );
      default:
        return <BankingDashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <EnterpriseHeader user={user} onLogout={logout} />
      <div className="flex">
        <EnterpriseSidebar activeTab={activeTab} onTabChange={setActiveTab} />
        <main className="flex-1">
          {renderContent()}
        </main>
      </div>
      <AdvancedChatBot />
    </div>
  );
};

// App progressive avec AuthProvider
const ProgressiveApp: React.FC = () => {
  return (
    <AuthProvider>
      <ProgressiveMainApp />
    </AuthProvider>
  );
};

export default ProgressiveApp;
