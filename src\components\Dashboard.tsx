import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Users, CreditCard, AlertTriangle, DollarSign } from 'lucide-react';
import StatCard from './StatCard';
import Chart from './Chart';
import { DatabaseService } from '../data/database';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState([
    {
      title: 'Clients Actifs',
      value: '0',
      change: '+0%',
      trend: 'up' as const,
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Crédits en Cours',
      value: '0 TND',
      change: '+0%',
      trend: 'up' as const,
      icon: CreditCard,
      color: 'green'
    },
    {
      title: 'Taux de Défaut',
      value: '0%',
      change: '+0%',
      trend: 'down' as const,
      icon: AlertTriangle,
      color: 'red'
    },
    {
      title: 'Revenus Mensuels',
      value: '0 TND',
      change: '+0%',
      trend: 'up' as const,
      icon: DollarSign,
      color: 'purple'
    }
  ]);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    // Charger les statistiques depuis la base de données
    const statistics = db.getStatistics();

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('fr-TN', {
        style: 'currency',
        currency: 'TND',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    };

    setStats([
      {
        title: 'Clients Actifs',
        value: statistics.activeClients.toString(),
        change: '+12.5%',
        trend: 'up' as const,
        icon: Users,
        color: 'blue'
      },
      {
        title: 'Crédits en Cours',
        value: formatCurrency(statistics.totalCredit),
        change: '+8.2%',
        trend: 'up' as const,
        icon: CreditCard,
        color: 'green'
      },
      {
        title: 'Taux de Défaut',
        value: `${statistics.defaultRate}%`,
        change: '-0.8%',
        trend: 'down' as const,
        icon: AlertTriangle,
        color: 'red'
      },
      {
        title: 'Score Moyen',
        value: statistics.averageScore.toString(),
        change: '+2.1%',
        trend: 'up' as const,
        icon: DollarSign,
        color: 'purple'
      }
    ]);
  }, []);

  const recentAlerts = [
    { id: 1, type: 'warning', message: 'Client Ahmed Ben Ali - Score de risque élevé', time: '2h ago' },
    { id: 2, type: 'info', message: 'Nouveau contrat généré pour Fatma Trabelsi', time: '4h ago' },
    { id: 3, type: 'error', message: 'Retard de paiement - Mohamed Cherif', time: '6h ago' },
  ];

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Tableau de Bord</h2>
        <p className="text-gray-600">Vue d'ensemble de votre activité bancaire</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Évolution des Crédits</h3>
          <Chart type="line" />
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition par Risque</h3>
          <Chart type="doughnut" />
        </div>
      </div>

      {/* Recent Alerts */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertes Récentes</h3>
        <div className="space-y-3">
          {recentAlerts.map((alert) => (
            <div key={alert.id} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
              <div className={`w-2 h-2 rounded-full ${
                alert.type === 'warning' ? 'bg-yellow-500' :
                alert.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
              }`} />
              <div className="flex-1">
                <p className="text-sm text-gray-900">{alert.message}</p>
                <p className="text-xs text-gray-500">{alert.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;