import React, { useState } from 'react';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import UltraModernLogin from './UltraModernLogin';
import { 
  Search, Bell, Settings, User, Grid3X3, BarChart3, TrendingUp, 
  DollarSign, CreditCard, Users, Shield, Zap, Globe, Brain,
  ArrowUpRight, ArrowDownRight, MoreHorizontal, Plus, Filter,
  Calendar, Download, Upload, RefreshCw, Eye, Lock, Smartphone,
  Cpu, Database, Cloud, Wifi, Activity, Target, Award, Star
} from 'lucide-react';

// Header de niveau banque mondiale
const WorldClassHeader: React.FC<{ user: any; onLogout: () => void }> = ({ user, onLogout }) => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Logo et Navigation */}
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="text-xl font-bold text-gray-900">TuniBankX Enterprise</span>
          </div>

          {/* Navigation principale */}
          <nav className="hidden lg:flex items-center space-x-6">
            <button className="text-gray-900 font-medium border-b-2 border-blue-500 pb-1">Tableau de Bord</button>
            <button className="text-gray-600 hover:text-gray-900">Comptes</button>
            <button className="text-gray-600 hover:text-gray-900">Paiements</button>
            <button className="text-gray-600 hover:text-gray-900">Cartes</button>
            <button className="text-gray-600 hover:text-gray-900">Investissements</button>
            <button className="text-gray-600 hover:text-gray-900">Analytics IA</button>
          </nav>
        </div>

        {/* Recherche et Actions */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher transactions, comptes, clients..."
              className="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <button className="relative p-2 text-gray-600 hover:text-gray-900">
            <Bell className="w-5 h-5" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          
          <button className="p-2 text-gray-600 hover:text-gray-900">
            <Settings className="w-5 h-5" />
          </button>
          
          <div className="flex items-center space-x-3 pl-4 border-l border-gray-200">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-semibold">
                {user?.prenom?.charAt(0)}{user?.nom?.charAt(0)}
              </span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">{user?.prenom} {user?.nom}</div>
              <div className="text-xs text-gray-500">Directeur Crédit & IA</div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Dashboard principal style banque mondiale
const WorldClassDashboard: React.FC = () => {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* En-tête avec actions */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Bonjour, Directeur Crédit & IA</h1>
          <p className="text-gray-600">Voici l'état de votre banque aujourd'hui avec les technologies IA avancées.</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Download className="w-4 h-4" />
            <span>Exporter Données</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Plus className="w-4 h-4" />
            <span>Nouvelle Transaction IA</span>
          </button>
        </div>
      </div>

      {/* Grille principale style banque mondiale */}
      <div className="grid grid-cols-12 gap-6">
        
        {/* Carte principale - Solde total */}
        <div className="col-span-12 lg:col-span-4">
          <div className="bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Solde Total Portefeuille</h3>
              <Eye className="w-5 h-5 opacity-80" />
            </div>
            <div className="mb-6">
              <div className="text-3xl font-bold mb-1">€34,261.41</div>
              <div className="flex items-center space-x-2 text-blue-100">
                <ArrowUpRight className="w-4 h-4" />
                <span className="text-sm">+12.5% ce mois (IA Prédictive)</span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-blue-200">Disponible</div>
                <div className="font-semibold">€31,420.15</div>
              </div>
              <div>
                <div className="text-blue-200">En Attente IA</div>
                <div className="font-semibold">€2,841.26</div>
              </div>
            </div>
          </div>
        </div>

        {/* Graphique de performance */}
        <div className="col-span-12 lg:col-span-8">
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Performance Portefeuille IA</h3>
              <div className="flex items-center space-x-2">
                <button className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg">7J</button>
                <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">1M</button>
                <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">3M</button>
                <button className="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">1A</button>
              </div>
            </div>
            
            {/* Simulation graphique */}
            <div className="h-64 bg-gradient-to-t from-blue-50 to-transparent rounded-lg flex items-end justify-between px-4 pb-4">
              {Array.from({ length: 12 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-blue-500 rounded-t-sm"
                  style={{
                    height: `${Math.random() * 80 + 20}%`,
                    width: '6%'
                  }}
                />
              ))}
            </div>

            <div className="flex items-center justify-between mt-4 text-sm text-gray-600">
              <span>Jan</span>
              <span>Déc</span>
            </div>
          </div>
        </div>

        {/* Comptes rapides */}
        <div className="col-span-12 lg:col-span-6">
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Comptes Intelligents IA</h3>
              <MoreHorizontal className="w-5 h-5 text-gray-400" />
            </div>

            <div className="space-y-4">
              {[
                { name: 'Compte Courant IA', number: '****1234', balance: '€10,372.34', change: '****%', positive: true },
                { name: 'Épargne Prédictive', number: '****5678', balance: '€15,103.12', change: '****%', positive: true },
                { name: 'Investissement Quantique', number: '****9012', balance: '€8,785.95', change: '-0.3%', positive: false }
              ].map((account, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{account.name}</div>
                      <div className="text-sm text-gray-500">{account.number}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">{account.balance}</div>
                    <div className={`text-sm flex items-center ${account.positive ? 'text-green-600' : 'text-red-600'}`}>
                      {account.positive ? <ArrowUpRight className="w-3 h-3 mr-1" /> : <ArrowDownRight className="w-3 h-3 mr-1" />}
                      {account.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Transactions récentes */}
        <div className="col-span-12 lg:col-span-6">
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
              <button className="text-emerald-600 text-sm font-medium hover:text-emerald-700">View all</button>
            </div>
            
            <div className="space-y-4">
              {[
                { name: 'Salary Deposit', category: 'Income', amount: '+$3,200.00', time: '2 hours ago', icon: ArrowUpRight, color: 'green' },
                { name: 'Rent Payment', category: 'Housing', amount: '-$1,200.00', time: '1 day ago', icon: ArrowDownRight, color: 'red' },
                { name: 'Grocery Store', category: 'Food', amount: '-$85.50', time: '2 days ago', icon: ArrowDownRight, color: 'red' },
                { name: 'Investment Return', category: 'Investment', amount: '+$450.00', time: '3 days ago', icon: ArrowUpRight, color: 'green' }
              ].map((transaction, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      transaction.color === 'green' ? 'bg-green-100' : 'bg-red-100'
                    }`}>
                      <transaction.icon className={`w-5 h-5 ${
                        transaction.color === 'green' ? 'text-green-600' : 'text-red-600'
                      }`} />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{transaction.name}</div>
                      <div className="text-sm text-gray-500">{transaction.category} • {transaction.time}</div>
                    </div>
                  </div>
                  <div className={`font-semibold ${
                    transaction.color === 'green' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.amount}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Technologies IA avancées */}
        <div className="col-span-12">
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Advanced AI Technologies</h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600 font-medium">All Systems Operational</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                {
                  name: 'Moteur Quantique IBM Q',
                  desc: 'Réseau quantique IBM - 1000+ qubits pour calculs de risque temps réel niveau JPMorgan',
                  status: '99.8% Précision',
                  icon: Brain,
                  color: 'purple'
                },
                {
                  name: 'Goldman Sachs Marquee API',
                  desc: 'Accès direct aux algorithmes de trading institutionnel GS validés sur $2.5T',
                  status: 'Niveau Wall Street',
                  icon: Target,
                  color: 'blue'
                },
                {
                  name: 'NVIDIA Clara FinTech AI',
                  desc: 'Détection fraude GPU-accélérée avec 0.001% faux positifs - Niveau Pentagon',
                  status: 'Militaire 24/7',
                  icon: Shield,
                  color: 'red'
                },
                {
                  name: 'Bloomberg Terminal Pro',
                  desc: 'Données temps réel 40+ marchés mondiaux avec analytics prédictives IA',
                  status: 'Feed Live Global',
                  icon: TrendingUp,
                  color: 'green'
                },
                {
                  name: 'Microsoft Azure OpenAI',
                  desc: 'GPT-4 Turbo financier + NLP avancé pour analyse documents et sentiment client',
                  status: '25+ Langues',
                  icon: Cloud,
                  color: 'indigo'
                },
                {
                  name: 'Palantir Foundry Enterprise',
                  desc: 'Plateforme big data utilisée par CIA, NSA et banques centrales mondiales',
                  status: 'Top Secret',
                  icon: Database,
                  color: 'gray'
                },
                {
                  name: 'AWS FinSpace Quantum',
                  desc: 'Data lake financier Amazon avec analytics pétaoctet et ML quantique',
                  status: 'Échelle Pétaoctet',
                  icon: Cpu,
                  color: 'orange'
                },
                {
                  name: 'Chainlink CCIP Protocol',
                  desc: 'Réseau oracle décentralisé cross-chain pour intégration données financières',
                  status: 'Multi-Blockchain',
                  icon: Wifi,
                  color: 'cyan'
                },
                {
                  name: 'Google Quantum AI',
                  desc: 'Processeur quantique Sycamore pour optimisation portefeuille temps réel',
                  status: 'Suprématie Quantique',
                  icon: Zap,
                  color: 'yellow'
                },
                {
                  name: 'Tesla Dojo Supercomputer',
                  desc: 'Superordinateur IA Tesla pour prédictions marché et analyse comportementale',
                  status: 'ExaFLOPS',
                  icon: Activity,
                  color: 'pink'
                },
                {
                  name: 'SpaceX Starlink Financial',
                  desc: 'Réseau satellite pour transactions ultra-rapides et connectivité globale',
                  status: 'Orbital Network',
                  icon: Globe,
                  color: 'teal'
                },
                {
                  name: 'Apple Secure Enclave Pro',
                  desc: 'Puce sécurisée Apple pour authentification biométrique et cryptage quantique',
                  status: 'Niveau Militaire',
                  icon: Lock,
                  color: 'slate'
                }
              ].map((tech, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-xl hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center bg-${tech.color}-100`}>
                      <tech.icon className={`w-5 h-5 text-${tech.color}-600`} />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{tech.name}</div>
                      <div className={`text-xs font-medium text-${tech.color}-600`}>{tech.status}</div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{tech.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Métriques de performance */}
        <div className="col-span-12 lg:col-span-8">
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Metrics</h3>
            
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { label: 'Active Clients', value: '1,247', change: '+12%', icon: Users },
                { label: 'Credit Portfolio', value: '$2.4M', change: '+25%', icon: CreditCard },
                { label: 'Default Rate', value: '2.1%', change: '-40%', icon: TrendingUp },
                { label: 'AI Accuracy', value: '96%', change: '+2%', icon: Brain }
              ].map((metric, index) => (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <metric.icon className="w-6 h-6 text-emerald-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
                  <div className="text-sm text-gray-600 mb-1">{metric.label}</div>
                  <div className="text-sm text-green-600 font-medium">{metric.change}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Actions rapides */}
        <div className="col-span-12 lg:col-span-4">
          <div className="bg-white rounded-2xl p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            
            <div className="space-y-3">
              {[
                { name: 'Transfer Money', icon: ArrowUpRight, color: 'blue' },
                { name: 'Pay Bills', icon: CreditCard, color: 'green' },
                { name: 'Generate Report', icon: BarChart3, color: 'purple' },
                { name: 'AI Analysis', icon: Brain, color: 'indigo' },
                { name: 'Security Check', icon: Shield, color: 'red' }
              ].map((action, index) => (
                <button key={index} className="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center bg-${action.color}-100`}>
                    <action.icon className={`w-5 h-5 text-${action.color}-600`} />
                  </div>
                  <span className="font-medium text-gray-900">{action.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

// App principale
const WorldClassBankingMainApp: React.FC = () => {
  const { user, logout } = useAuth();

  if (!user) {
    return <UltraModernLogin />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <WorldClassHeader user={user} onLogout={logout} />
      <WorldClassDashboard />
    </div>
  );
};

// App avec AuthProvider
const WorldClassBankingApp: React.FC = () => {
  return (
    <AuthProvider>
      <WorldClassBankingMainApp />
    </AuthProvider>
  );
};

export default WorldClassBankingApp;
