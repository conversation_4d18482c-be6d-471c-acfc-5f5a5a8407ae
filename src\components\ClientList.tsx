import React, { useState, useEffect } from 'react';
import { Search, Filter, MapPin, TrendingUp, TrendingDown, Eye, FileText } from 'lucide-react';
import { DatabaseService, Client } from '../data/database';

const ClientList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRisk, setFilterRisk] = useState('all');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const [clients, setClients] = useState<Client[]>([]);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    // Charger les clients depuis la base de données
    const loadedClients = db.getAllClients();
    setClients(loadedClients);
  }, []);

  const getRiskColor = (score: number) => {
    if (score >= 70) return 'text-green-600 bg-green-50';
    if (score >= 50) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getRiskLabel = (score: number) => {
    if (score >= 70) return 'Faible';
    if (score >= 50) return 'Modéré';
    return 'Élevé';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'actif': return 'text-green-600 bg-green-50';
      case 'en_defaut': return 'text-red-600 bg-red-50';
      case 'suspendu': return 'text-orange-600 bg-orange-50';
      case 'inactif': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'actif': return 'Actif';
      case 'en_defaut': return 'En défaut';
      case 'suspendu': return 'Suspendu';
      case 'inactif': return 'Inactif';
      default: return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const filteredClients = clients.filter(client => {
    const matchesSearch = `${client.prenom} ${client.nom}`.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRisk = filterRisk === 'all' ||
      (filterRisk === 'low' && client.scoreRisque >= 70) ||
      (filterRisk === 'medium' && client.scoreRisque >= 50 && client.scoreRisque < 70) ||
      (filterRisk === 'high' && client.scoreRisque < 50);

    return matchesSearch && matchesRisk;
  });

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Gestion des Clients</h2>
        <p className="text-gray-600">Gérez et analysez votre portefeuille clients</p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher par nom, prénom..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={filterRisk}
              onChange={(e) => setFilterRisk(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Tous les risques</option>
              <option value="low">Risque faible</option>
              <option value="medium">Risque modéré</option>
              <option value="high">Risque élevé</option>
            </select>
          </div>
        </div>
      </div>

      {/* Client Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Risque</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Crédit en Cours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Localisation</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredClients.map((client) => (
                <tr key={client.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium text-sm">
                          {client.prenom[0]}{client.nom[0]}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {client.prenom} {client.nom}
                        </div>
                        <div className="text-sm text-gray-500">{client.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskColor(client.scoreRisque)}`}>
                        {getRiskLabel(client.scoreRisque)}
                      </span>
                      <span className="text-sm font-medium text-gray-900">{client.scoreRisque}/100</span>
                      {client.scoreRisque >= 70 ? 
                        <TrendingUp className="w-4 h-4 text-green-500" /> : 
                        <TrendingDown className="w-4 h-4 text-red-500" />
                      }
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {client.creditEnCours}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="w-4 h-4 mr-1" />
                      {client.localisation}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(client.statut)}`}>
                      {client.statut}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => setSelectedClient(client)}
                        className="text-blue-600 hover:text-blue-900 transition-colors"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900 transition-colors">
                        <FileText className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Client Detail Modal */}
      {selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-gray-900">
                Profil Client - {selectedClient.prenom} {selectedClient.nom}
              </h3>
              <button 
                onClick={() => setSelectedClient(null)}
                className="text-gray-400 hover:text-gray-600 text-xl"
              >
                ×
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Informations Personnelles</label>
                  <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                    <p><span className="font-medium">Nom:</span> {selectedClient.nom}</p>
                    <p><span className="font-medium">Prénom:</span> {selectedClient.prenom}</p>
                    <p><span className="font-medium">Email:</span> {selectedClient.email}</p>
                    <p><span className="font-medium">Téléphone:</span> {selectedClient.telephone}</p>
                    <p><span className="font-medium">Localisation:</span> {selectedClient.localisation}</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Score de Risque</label>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-2xl font-bold text-gray-900">{selectedClient.scoreRisque}/100</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(selectedClient.scoreRisque)}`}>
                        {getRiskLabel(selectedClient.scoreRisque)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${selectedClient.scoreRisque >= 70 ? 'bg-green-500' : selectedClient.scoreRisque >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`}
                        style={{ width: `${selectedClient.scoreRisque}%` }}
                      />
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Crédit Actuel</label>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-xl font-bold text-blue-600">{selectedClient.creditEnCours}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end space-x-3">
              <button 
                onClick={() => setSelectedClient(null)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Fermer
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Générer Contrat
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientList;