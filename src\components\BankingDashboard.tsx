import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, TrendingDown, Users, CreditCard, AlertTriangle, DollarSign,
  Activity, Shield, Target, Zap, Bell, Eye, BarChart3, PieChart,
  ArrowUpRight, ArrowDownRight, Clock, CheckCircle, XCircle, AlertCircle
} from 'lucide-react';
import { DatabaseService, mockAlertes, mockTransactions } from '../data/database';

interface KPI {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ElementType;
  color: string;
  description: string;
}

interface AlerteItem {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  time: string;
  priority: 'high' | 'medium' | 'low';
}

const BankingDashboard: React.FC = () => {
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [alertes, setAlertes] = useState<AlerteItem[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Simulation du chargement
      await new Promise(resolve => setTimeout(resolve, 1000));

      const statistics = db.getStatistics();
      
      // KPIs bancaires avancés
      const kpiData: KPI[] = [
        {
          title: 'Clients Actifs',
          value: statistics.activeClients.toString(),
          change: '+12.5%',
          trend: 'up',
          icon: Users,
          color: 'blue',
          description: 'Clients avec comptes actifs'
        },
        {
          title: 'Portefeuille Crédit',
          value: `${(statistics.totalCredit / 1000).toFixed(0)}K TND`,
          change: '+8.2%',
          trend: 'up',
          icon: CreditCard,
          color: 'green',
          description: 'Total des crédits en cours'
        },
        {
          title: 'Taux de Défaut',
          value: `${statistics.defaultRate}%`,
          change: '-0.8%',
          trend: 'down',
          icon: AlertTriangle,
          color: 'red',
          description: 'Pourcentage de crédits en défaut'
        },
        {
          title: 'Score Moyen',
          value: statistics.averageScore.toString(),
          change: '+2.1%',
          trend: 'up',
          icon: Target,
          color: 'purple',
          description: 'Score de risque moyen'
        },
        {
          title: 'ROA',
          value: '2.8%',
          change: '+0.3%',
          trend: 'up',
          icon: TrendingUp,
          color: 'indigo',
          description: 'Return on Assets'
        },
        {
          title: 'Liquidité',
          value: '18.5%',
          change: '+1.2%',
          trend: 'up',
          icon: Activity,
          color: 'cyan',
          description: 'Ratio de liquidité'
        },
        {
          title: 'PNB',
          value: '2.1M TND',
          change: '+15.3%',
          trend: 'up',
          icon: DollarSign,
          color: 'emerald',
          description: 'Produit Net Bancaire'
        },
        {
          title: 'Conformité',
          value: '98.2%',
          change: '+0.5%',
          trend: 'up',
          icon: Shield,
          color: 'orange',
          description: 'Taux de conformité réglementaire'
        }
      ];

      // Alertes en temps réel
      const alertesData: AlerteItem[] = [
        {
          id: '1',
          type: 'warning',
          title: 'Retard de Paiement',
          message: 'Client Mohamed Cherif - Retard de 15 jours',
          time: '2h',
          priority: 'high'
        },
        {
          id: '2',
          type: 'error',
          title: 'Transaction Suspecte',
          message: 'Transaction de 5000 TND détectée à l\'étranger',
          time: '4h',
          priority: 'high'
        },
        {
          id: '3',
          type: 'success',
          title: 'Nouveau Crédit Approuvé',
          message: 'Crédit immobilier de 80K TND approuvé pour Leila Karray',
          time: '6h',
          priority: 'medium'
        },
        {
          id: '4',
          type: 'info',
          title: 'Limite de Découvert',
          message: 'Ahmed Ben Ali approche de sa limite de découvert',
          time: '8h',
          priority: 'low'
        },
        {
          id: '5',
          type: 'warning',
          title: 'Score de Risque',
          message: 'Dégradation du score pour 3 clients ce mois',
          time: '1j',
          priority: 'medium'
        }
      ];

      // Transactions récentes
      const transactionsData = mockTransactions.slice(0, 5).map(t => ({
        ...t,
        clientName: t.compteID === 'CPT001' ? 'Ahmed Ben Ali' : 'Fatma Trabelsi'
      }));

      setKpis(kpiData);
      setAlertes(alertesData);
      setRecentTransactions(transactionsData);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setLoading(false);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle;
      case 'warning': return AlertCircle;
      case 'error': return XCircle;
      default: return Bell;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return ArrowUpRight;
      case 'down': return ArrowDownRight;
      default: return Activity;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête avec indicateurs temps réel */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Tableau de Bord Bancaire</h2>
          <p className="text-gray-600 flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            Données en temps réel - Dernière mise à jour: {new Date().toLocaleTimeString('fr-FR')}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium text-gray-700">Système Actif</span>
            </div>
          </div>
          <div className="bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium text-gray-700">Sécurisé</span>
            </div>
          </div>
        </div>
      </div>

      {/* KPIs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpis.map((kpi, index) => {
          const Icon = kpi.icon;
          const TrendIcon = getTrendIcon(kpi.trend);
          
          return (
            <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center bg-${kpi.color}-100`}>
                  <Icon className={`w-6 h-6 text-${kpi.color}-600`} />
                </div>
                <div className={`flex items-center space-x-1 ${getTrendColor(kpi.trend)}`}>
                  <TrendIcon className="w-4 h-4" />
                  <span className="text-sm font-medium">{kpi.change}</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{kpi.title}</p>
                <p className="text-2xl font-bold text-gray-900 mb-1">{kpi.value}</p>
                <p className="text-xs text-gray-500">{kpi.description}</p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Contenu principal en deux colonnes */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alertes et Notifications */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Bell className="w-5 h-5 mr-2 text-orange-500" />
                Alertes Temps Réel
              </h3>
              <span className="bg-red-100 text-red-600 text-xs font-medium px-2 py-1 rounded-full">
                {alertes.filter(a => a.priority === 'high').length} Urgentes
              </span>
            </div>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {alertes.map((alerte) => {
                const AlertIcon = getAlertIcon(alerte.type);
                return (
                  <div key={alerte.id} className={`p-4 rounded-lg border ${getAlertColor(alerte.type)}`}>
                    <div className="flex items-start space-x-3">
                      <AlertIcon className="w-5 h-5 mt-0.5" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{alerte.title}</p>
                          <span className="text-xs text-gray-500">{alerte.time}</span>
                        </div>
                        <p className="text-sm mt-1">{alerte.message}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            alerte.priority === 'high' ? 'bg-red-100 text-red-600' :
                            alerte.priority === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {alerte.priority === 'high' ? 'Urgent' : 
                             alerte.priority === 'medium' ? 'Moyen' : 'Faible'}
                          </span>
                          <button className="text-xs text-blue-600 hover:text-blue-800 font-medium">
                            Traiter
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Graphiques et Analytics */}
        <div className="lg:col-span-2 space-y-6">
          {/* Transactions Récentes */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Activity className="w-5 h-5 mr-2 text-blue-500" />
                Transactions Récentes
              </h3>
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center">
                Voir tout <Eye className="w-4 h-4 ml-1" />
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-3">Client</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-3">Type</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-3">Montant</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-3">Statut</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider pb-3">Date</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {recentTransactions.map((transaction, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="py-3">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <span className="text-blue-600 font-medium text-sm">
                              {transaction.clientName?.split(' ').map((n: string) => n[0]).join('')}
                            </span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{transaction.clientName}</span>
                        </div>
                      </td>
                      <td className="py-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          transaction.type === 'credit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {transaction.type === 'credit' ? 'Crédit' : 'Débit'}
                        </span>
                      </td>
                      <td className="py-3 text-sm font-medium text-gray-900">
                        {transaction.montant.toLocaleString()} TND
                      </td>
                      <td className="py-3">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Exécuté
                        </span>
                      </td>
                      <td className="py-3 text-sm text-gray-500">
                        {new Date(transaction.dateTransaction).toLocaleDateString('fr-FR')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Graphiques de Performance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-purple-500" />
                Évolution Mensuelle
              </h3>
              <div className="h-48 flex items-end justify-between space-x-2">
                {[65, 78, 82, 88, 92, 85, 90].map((height, index) => (
                  <div key={index} className="flex-1 bg-gradient-to-t from-purple-500 to-purple-300 rounded-t" 
                       style={{ height: `${height}%` }}>
                  </div>
                ))}
              </div>
              <div className="flex justify-between mt-2 text-xs text-gray-500">
                <span>Jan</span><span>Fév</span><span>Mar</span><span>Avr</span><span>Mai</span><span>Jun</span><span>Jul</span>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <PieChart className="w-5 h-5 mr-2 text-green-500" />
                Répartition Portefeuille
              </h3>
              <div className="h-48 flex items-center justify-center">
                <div className="relative w-32 h-32">
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none" stroke="#e5e7eb" strokeWidth="3"/>
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none" stroke="#10b981" strokeWidth="3" strokeDasharray="60, 100"/>
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-2xl font-bold text-gray-900">60%</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2 mt-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">Immobilier</span>
                  </div>
                  <span className="text-sm font-medium">60%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">Personnel</span>
                  </div>
                  <span className="text-sm font-medium">25%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">Auto</span>
                  </div>
                  <span className="text-sm font-medium">15%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankingDashboard;
