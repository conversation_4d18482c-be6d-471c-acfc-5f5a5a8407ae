import React, { useState, useEffect } from 'react';
import { 
  Search, Filter, Plus, Eye, Edit, Trash2, Download, Upload, 
  User, Phone, Mail, MapPin, Briefcase, CreditCard, TrendingUp,
  TrendingDown, AlertTriangle, CheckCircle, Clock, Star,
  FileText, Camera, Shield, Target, Activity, DollarSign
} from 'lucide-react';
import { DatabaseService, Client, mockComptes, mockTransactions } from '../data/database';

interface ClientProfile extends Client {
  totalSolde: number;
  nombreComptes: number;
  dernierTransaction: string;
  scoreEvolution: 'up' | 'down' | 'stable';
  segmentColor: string;
  risqueLevel: 'low' | 'medium' | 'high';
}

const AdvancedClientManagement: React.FC = () => {
  const [clients, setClients] = useState<ClientProfile[]>([]);
  const [filteredClients, setFilteredClients] = useState<ClientProfile[]>([]);
  const [selectedClient, setSelectedClient] = useState<ClientProfile | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSegment, setFilterSegment] = useState('all');
  const [filterRisk, setFilterRisk] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showClientModal, setShowClientModal] = useState(false);
  const [loading, setLoading] = useState(true);

  const db = DatabaseService.getInstance();

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [clients, searchTerm, filterSegment, filterRisk, filterStatus]);

  const loadClients = async () => {
    try {
      const clientsData = db.getAllClients();
      
      // Enrichir les données clients avec des informations bancaires
      const enrichedClients: ClientProfile[] = clientsData.map(client => {
        const clientComptes = mockComptes.filter(c => c.clientID === client.clientID);
        const totalSolde = clientComptes.reduce((sum, compte) => sum + compte.solde, 0);
        const clientTransactions = mockTransactions.filter(t => 
          clientComptes.some(c => c.compteID === t.compteID)
        );
        
        const lastTransaction = clientTransactions.length > 0 
          ? clientTransactions.sort((a, b) => new Date(b.dateTransaction).getTime() - new Date(a.dateTransaction).getTime())[0]
          : null;

        return {
          ...client,
          totalSolde,
          nombreComptes: clientComptes.length,
          dernierTransaction: lastTransaction ? lastTransaction.dateTransaction : 'Aucune',
          scoreEvolution: Math.random() > 0.5 ? 'up' : Math.random() > 0.5 ? 'down' : 'stable',
          segmentColor: getSegmentColor(client.segmentClient),
          risqueLevel: client.scoreRisque >= 70 ? 'low' : client.scoreRisque >= 50 ? 'medium' : 'high'
        };
      });

      setClients(enrichedClients);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement des clients:', error);
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = clients;

    // Filtre par recherche
    if (searchTerm) {
      filtered = filtered.filter(client =>
        `${client.prenom} ${client.nom}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.cin.includes(searchTerm) ||
        client.telephone.includes(searchTerm)
      );
    }

    // Filtre par segment
    if (filterSegment !== 'all') {
      filtered = filtered.filter(client => client.segmentClient === filterSegment);
    }

    // Filtre par risque
    if (filterRisk !== 'all') {
      filtered = filtered.filter(client => client.risqueLevel === filterRisk);
    }

    // Filtre par statut
    if (filterStatus !== 'all') {
      filtered = filtered.filter(client => client.status === filterStatus);
    }

    setFilteredClients(filtered);
  };

  const getSegmentColor = (segment: string) => {
    switch (segment) {
      case 'premium': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'professionnel': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'entreprise': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'actif': return 'text-green-600 bg-green-50 border-green-200';
      case 'en_defaut': return 'text-red-600 bg-red-50 border-red-200';
      case 'suspendu': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'inactif': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getScoreEvolutionIcon = (evolution: string) => {
    switch (evolution) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Clients</h2>
          <p className="text-gray-600">
            {filteredClients.length} clients sur {clients.length} total
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="w-4 h-4 mr-2" />
            Nouveau Client
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <Upload className="w-4 h-4 mr-2" />
            Importer
          </button>
          <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Exporter
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Clients</p>
              <p className="text-2xl font-bold text-gray-900">{clients.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <User className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Clients Premium</p>
              <p className="text-2xl font-bold text-gray-900">
                {clients.filter(c => c.segmentClient === 'premium').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Risque Élevé</p>
              <p className="text-2xl font-bold text-gray-900">
                {clients.filter(c => c.risqueLevel === 'high').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Portefeuille Total</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(clients.reduce((sum, c) => sum + c.totalSolde, 0))}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher un client..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <select
            value={filterSegment}
            onChange={(e) => setFilterSegment(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tous les segments</option>
            <option value="particulier">Particulier</option>
            <option value="professionnel">Professionnel</option>
            <option value="entreprise">Entreprise</option>
            <option value="premium">Premium</option>
          </select>

          <select
            value={filterRisk}
            onChange={(e) => setFilterRisk(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tous les risques</option>
            <option value="low">Risque Faible</option>
            <option value="medium">Risque Moyen</option>
            <option value="high">Risque Élevé</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="actif">Actif</option>
            <option value="en_defaut">En Défaut</option>
            <option value="suspendu">Suspendu</option>
            <option value="inactif">Inactif</option>
          </select>

          <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center">
            <Filter className="w-4 h-4 mr-2" />
            Filtres Avancés
          </button>
        </div>
      </div>

      {/* Liste des clients */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Segment</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Risque</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portefeuille</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dernière Activité</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredClients.map((client) => (
                <tr key={client.clientID} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                        <span className="text-white font-medium text-sm">
                          {client.prenom[0]}{client.nom[0]}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {client.prenom} {client.nom}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail className="w-3 h-3 mr-1" />
                          {client.email}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          {client.telephone}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${client.segmentColor}`}>
                      {client.segmentClient}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <div className={`flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getRiskColor(client.risqueLevel)}`}>
                        <Target className="w-3 h-3 mr-1" />
                        {client.scoreRisque}
                      </div>
                      {getScoreEvolutionIcon(client.scoreEvolution)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(client.totalSolde)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {client.nombreComptes} compte{client.nombreComptes > 1 ? 's' : ''}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(client.status)}`}>
                      {client.status === 'actif' ? 'Actif' : 
                       client.status === 'en_defaut' ? 'En Défaut' :
                       client.status === 'suspendu' ? 'Suspendu' : 'Inactif'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {client.dernierTransaction !== 'Aucune' 
                      ? new Date(client.dernierTransaction).toLocaleDateString('fr-FR')
                      : 'Aucune'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => setSelectedClient(client)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Voir le profil"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900 p-1 rounded" title="Modifier">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-900 p-1 rounded" title="Supprimer">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de profil client */}
      {selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900">
                  Profil Client - {selectedClient.prenom} {selectedClient.nom}
                </h3>
                <button 
                  onClick={() => setSelectedClient(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              {/* Informations personnelles */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">Informations Personnelles</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <User className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">CIN:</span>
                      <span className="text-sm font-medium text-gray-900 ml-2">{selectedClient.cin}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">Date de naissance:</span>
                      <span className="text-sm font-medium text-gray-900 ml-2">
                        {new Date(selectedClient.dateNaissance).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">Adresse:</span>
                      <span className="text-sm font-medium text-gray-900 ml-2">{selectedClient.adresse}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900">Informations Professionnelles</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Briefcase className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">Profession:</span>
                      <span className="text-sm font-medium text-gray-900 ml-2">{selectedClient.profession}</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">Revenu mensuel:</span>
                      <span className="text-sm font-medium text-gray-900 ml-2">
                        {formatCurrency(selectedClient.revenuMensuel)}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-sm text-gray-600">Ancienneté:</span>
                      <span className="text-sm font-medium text-gray-900 ml-2">
                        {selectedClient.ancienneteEmploi} ans
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Analyse de risque */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Analyse de Risque</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedClient.scoreRisque}</div>
                    <div className="text-sm text-gray-600">Score de Risque</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(selectedClient.patrimoine)}
                    </div>
                    <div className="text-sm text-gray-600">Patrimoine</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatCurrency(selectedClient.chargesMensuelles)}
                    </div>
                    <div className="text-sm text-gray-600">Charges Mensuelles</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedClientManagement;
