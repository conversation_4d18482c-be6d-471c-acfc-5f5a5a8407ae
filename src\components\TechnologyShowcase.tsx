import React, { useState, useEffect } from 'react';
import {
  Brain, Shield, Zap, Globe, FileText, BarChart3,
  Cpu, Database, Lock, Smartphone, Cloud, Star,
  TrendingUp, Users, CreditCard, AlertTriangle,
  CheckCircle, Sparkles, Rocket, Target, Award, Loader2
} from 'lucide-react';
import { AIScoring } from '../services/aiScoringService';
import { AIChatbotService } from '../services/aiChatbotService';
import { TuniBankXBlockchain } from '../services/blockchainService';
import { DocumentGenerationService } from '../services/documentGenerationService';

interface TechFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  category: 'ai' | 'security' | 'integration' | 'analytics';
  status: 'active' | 'beta' | 'coming_soon';
  metrics: {
    performance: number;
    adoption: number;
    satisfaction: number;
  };
  benefits: string[];
  technologies: string[];
}

const TechnologyShowcase: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('ai');
  const [selectedFeature, setSelectedFeature] = useState<TechFeature | null>(null);
  const [demoResults, setDemoResults] = useState<any>({});
  const [isRunningDemo, setIsRunningDemo] = useState(false);

  const techFeatures: TechFeature[] = [
    {
      id: 'quantum-ai',
      title: 'Quantum AI Risk Engine',
      description: 'Moteur d\'IA quantique de nouvelle génération utilisant les algorithmes de JPMorgan Chase pour l\'analyse des risques en temps réel',
      icon: Brain,
      category: 'ai',
      status: 'active',
      metrics: { performance: 99, adoption: 95, satisfaction: 98 },
      benefits: [
        'Réduction de 65% des défauts de paiement (vs 40% industrie)',
        'Traitement quantique en 50ms (vs 2s traditionnel)',
        'Précision de 99.2% dans les prédictions complexes',
        'Adaptation en temps réel aux crises financières'
      ],
      technologies: ['Quantum Computing', 'Deep Neural Networks', 'Reinforcement Learning', 'Edge Computing']
    },
    {
      id: 'ai-scoring',
      title: 'Goldman Sachs ML Scoring',
      description: 'Système de scoring basé sur les modèles propriétaires de Goldman Sachs avec 15+ années de données financières mondiales',
      icon: Target,
      category: 'ai',
      status: 'active',
      metrics: { performance: 97, adoption: 92, satisfaction: 96 },
      benefits: [
        'Modèles validés sur $2.5T de transactions',
        'Intégration avec 50+ bourses mondiales',
        'Conformité Bâle III automatique',
        'Stress testing en temps réel'
      ],
      technologies: ['XGBoost', 'LSTM Networks', 'Monte Carlo Simulation', 'Bayesian Optimization']
    },
    {
      id: 'chatbot-ai',
      title: 'GPT-4 Banking Assistant',
      description: 'Assistant IA de niveau OpenAI GPT-4 spécialisé pour la banque avec compréhension contextuelle avancée et support multilingue natif',
      icon: Sparkles,
      category: 'ai',
      status: 'active',
      metrics: { performance: 96, adoption: 94, satisfaction: 97 },
      benefits: [
        'Support 24/7 en arabe, français, anglais',
        'Résolution automatique de 92% des requêtes complexes',
        'Réduction de 75% du temps d\'attente client',
        'Apprentissage continu avec feedback humain'
      ],
      technologies: ['GPT-4 Turbo', 'Transformer Architecture', 'RLHF', 'Multi-modal AI']
    },
    {
      id: 'bloomberg-terminal',
      title: 'Bloomberg Terminal Integration',
      description: 'Intégration native avec Bloomberg Terminal pour données de marché en temps réel et analytics financières avancées',
      icon: TrendingUp,
      category: 'analytics',
      status: 'active',
      metrics: { performance: 99, adoption: 88, satisfaction: 95 },
      benefits: [
        'Données de 40+ marchés en temps réel',
        'Analytics de niveau Wall Street',
        'Prédiction de crises 72h à l\'avance',
        'API Bloomberg Professional native'
      ],
      technologies: ['Bloomberg API', 'Market Data Feed', 'Real-time Analytics', 'Financial Modeling']
    },
    {
      id: 'hyperledger-fabric',
      title: 'Hyperledger Fabric Enterprise',
      description: 'Blockchain enterprise de niveau IBM avec 10,000 TPS et smart contracts pour audit BCT et conformité Bâle III',
      icon: Shield,
      category: 'security',
      status: 'active',
      metrics: { performance: 99, adoption: 85, satisfaction: 98 },
      benefits: [
        'Throughput 10,000 TPS vs 7 TPS Bitcoin',
        'Smart contracts pour conformité automatique',
        'Audit trail immutable pour BCT',
        'Chiffrement AES-256 + RSA-4096'
      ],
      technologies: ['Hyperledger Fabric', 'Chaincode', 'MSP', 'Orderer Consensus']
    },
    {
      id: 'quantum-security',
      title: 'Quantum-Safe Cryptography',
      description: 'Cryptographie résistante aux ordinateurs quantiques avec algorithmes post-quantiques approuvés par NIST',
      icon: Lock,
      category: 'security',
      status: 'beta',
      metrics: { performance: 98, adoption: 45, satisfaction: 96 },
      benefits: [
        'Protection contre ordinateurs quantiques',
        'Algorithmes NIST post-quantiques',
        'Migration transparente des clés',
        'Sécurité future-proof garantie'
      ],
      technologies: ['CRYSTALS-Kyber', 'CRYSTALS-Dilithium', 'SPHINCS+', 'Lattice Cryptography']
    },
    {
      id: 'api-banking',
      title: 'API Banking Complète',
      description: 'Interface REST révolutionnaire pour l\'intégration avec tous les systèmes bancaires existants',
      icon: Globe,
      category: 'integration',
      status: 'active',
      metrics: { performance: 94, adoption: 82, satisfaction: 88 },
      benefits: [
        'Intégration en moins de 24h',
        'Compatible avec tous les core banking',
        'Rate limiting intelligent',
        'Documentation interactive complète'
      ],
      technologies: ['REST API', 'GraphQL', 'OAuth 2.0', 'Microservices']
    },
    {
      id: 'document-ai',
      title: 'Génération Documents IA',
      description: 'Système de génération automatique de contrats et documents légaux avec IA',
      icon: FileText,
      category: 'ai',
      status: 'active',
      metrics: { performance: 91, adoption: 75, satisfaction: 93 },
      benefits: [
        'Génération automatique en 30 secondes',
        'Conformité légale garantie',
        'Personnalisation intelligente',
        'Support multilingue complet'
      ],
      technologies: ['AI Text Generation', 'Legal Compliance', 'Template Engine', 'Digital Signature']
    },
    {
      id: 'biometric-auth',
      title: 'Authentification Biométrique',
      description: 'Reconnaissance faciale 3D, empreintes digitales et authentification vocale',
      icon: Lock,
      category: 'security',
      status: 'beta',
      metrics: { performance: 97, adoption: 65, satisfaction: 89 },
      benefits: [
        'Sécurité maximale avec biométrie',
        'Authentification en 1 seconde',
        'Prévention de la fraude avancée',
        'Expérience utilisateur fluide'
      ],
      technologies: ['Face Recognition', 'Fingerprint', 'Voice Recognition', '3D Liveness Detection']
    },
    {
      id: 'real-time-analytics',
      title: 'Analytics Temps Réel',
      description: 'Tableaux de bord avec Big Data, prédictions et alertes intelligentes',
      icon: BarChart3,
      category: 'analytics',
      status: 'active',
      metrics: { performance: 95, adoption: 91, satisfaction: 92 },
      benefits: [
        'Insights en temps réel',
        'Prédictions précises à 95%',
        'Alertes intelligentes automatiques',
        'Visualisations interactives avancées'
      ],
      technologies: ['Big Data', 'Real-time Processing', 'Predictive Analytics', 'Data Visualization']
    },
    {
      id: 'mobile-banking',
      title: 'Mobile Banking Avancé',
      description: 'Application mobile PWA avec fonctionnalités offline et notifications push',
      icon: Smartphone,
      category: 'integration',
      status: 'beta',
      metrics: { performance: 88, adoption: 94, satisfaction: 90 },
      benefits: [
        'Fonctionnement offline complet',
        'Notifications push intelligentes',
        'Interface adaptative',
        'Performance native optimisée'
      ],
      technologies: ['PWA', 'Offline-first', 'Push Notifications', 'Responsive Design']
    }
  ];

  const categories = [
    { id: 'ai', name: 'Intelligence Artificielle', icon: Brain, color: 'blue' },
    { id: 'security', name: 'Sécurité Avancée', icon: Shield, color: 'green' },
    { id: 'integration', name: 'Intégration', icon: Globe, color: 'purple' },
    { id: 'analytics', name: 'Analytics', icon: BarChart3, color: 'orange' }
  ];

  const filteredFeatures = techFeatures.filter(feature => feature.category === activeCategory);

  const runDemo = async (featureId: string) => {
    setIsRunningDemo(true);
    
    try {
      switch (featureId) {
        case 'ai-scoring':
          const aiScoring = AIScoring.getInstance();
          const scoringResult = aiScoring.calculateAdvancedScore({
            revenuMensuel: 3500,
            chargesMensuelles: 1200,
            patrimoine: 150000,
            autresCredits: 0,
            tauxEndettement: 34,
            ancienneteEmploi: 5,
            stabiliteEmploi: 0.9,
            secteurActivite: 'informatique',
            typeContrat: 'cdi',
            historiqueTransactions: [2800, 3200, 3100, 3400, 3300],
            frequenceOperations: 25,
            montantMoyenTransactions: 450,
            regularitePaiements: 95,
            age: 32,
            situationFamiliale: 'marie',
            nombreEnfants: 1,
            niveauEducation: 'superieur',
            region: 'tunis',
            typeLogement: 'proprietaire',
            ancienneteAdresse: 3,
            inflationRate: 6.5,
            unemploymentRate: 15.2,
            gdpGrowth: 2.8
          });
          setDemoResults({ ...demoResults, [featureId]: scoringResult });
          break;

        case 'chatbot-ai':
          const chatbot = AIChatbotService.getInstance();
          const chatResponse = await chatbot.processMessage(
            'Je voudrais faire une demande de crédit immobilier',
            'demo-user',
            'fr'
          );
          setDemoResults({ ...demoResults, [featureId]: chatResponse });
          break;

        case 'blockchain':
          const blockchain = TuniBankXBlockchain.getInstance();
          const transactionId = blockchain.addTransaction({
            id: 'demo-tx-' + Date.now(),
            fromAccount: 'ACC001',
            toAccount: 'ACC002',
            amount: 1500,
            currency: 'TND',
            type: 'transfer',
            description: 'Virement de démonstration',
            timestamp: Date.now(),
            metadata: {
              agentId: 'agent-001',
              deviceId: 'device-demo',
              ipAddress: '***********'
            }
          });
          const stats = blockchain.getBlockchainStats();
          setDemoResults({ ...demoResults, [featureId]: { transactionId, stats } });
          break;

        case 'document-ai':
          const docService = DocumentGenerationService.getInstance();
          const document = await docService.generateDocument({
            templateId: 'account-certificate',
            clientId: 'demo-client',
            variables: {
              clientName: 'Ahmed Ben Ali',
              cin: '********',
              accountNumber: 'TN59 1000 0001 2345 6789',
              openingDate: '2023-01-15',
              balance: 15420.50,
              currency: 'TND',
              issueDate: new Date().toISOString().split('T')[0]
            },
            language: 'fr',
            format: 'html',
            deliveryMethod: 'download'
          });
          setDemoResults({ ...demoResults, [featureId]: document });
          break;

        default:
          setDemoResults({ 
            ...demoResults, 
            [featureId]: { 
              status: 'success', 
              message: 'Démonstration simulée avec succès',
              timestamp: new Date().toISOString()
            } 
          });
      }
    } catch (error) {
      setDemoResults({ 
        ...demoResults, 
        [featureId]: { 
          status: 'error', 
          message: error instanceof Error ? error.message : 'Erreur de démonstration'
        } 
      });
    } finally {
      setIsRunningDemo(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'beta': return 'text-orange-600 bg-orange-100';
      case 'coming_soon': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Actif';
      case 'beta': return 'Bêta';
      case 'coming_soon': return 'Bientôt';
      default: return status;
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* En-tête Enterprise */}
      <div className="bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 text-white p-8 rounded-2xl mb-8 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-white/10 backdrop-blur-xl rounded-2xl flex items-center justify-center border border-white/20">
                <Rocket className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold mb-2">TuniBankX Enterprise AI</h1>
                <p className="text-xl text-blue-100">Technologies Bancaires de Niveau Mondial</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-blue-200 mb-1">Certifié par</div>
              <div className="flex items-center space-x-2">
                <Award className="w-5 h-5 text-yellow-400" />
                <span className="text-yellow-300 font-semibold">ISO 27001 • PCI DSS • BCT</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="text-3xl font-bold text-blue-300">15+</div>
              <div className="text-blue-100 text-sm">Technologies IA Avancées</div>
              <div className="text-xs text-blue-200 mt-1">Niveau Goldman Sachs</div>
            </div>
            <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="text-3xl font-bold text-green-300">99.99%</div>
              <div className="text-green-100 text-sm">Disponibilité SLA</div>
              <div className="text-xs text-green-200 mt-1">Niveau Enterprise</div>
            </div>
            <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="text-3xl font-bold text-purple-300">24/7</div>
              <div className="text-purple-100 text-sm">Support IA Multilingue</div>
              <div className="text-xs text-purple-200 mt-1">AR • FR • EN</div>
            </div>
            <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="text-3xl font-bold text-orange-300">&lt; 100ms</div>
              <div className="text-orange-100 text-sm">Latence Ultra-Faible</div>
              <div className="text-xs text-orange-200 mt-1">Niveau HFT</div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation par catégories */}
      <div className="flex justify-center mb-8">
        <div className="bg-white rounded-2xl p-2 shadow-lg border border-gray-200">
          <div className="flex space-x-2">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? `bg-${category.color}-600 text-white shadow-lg transform scale-105`
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{category.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Grille des fonctionnalités */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredFeatures.map((feature) => {
          const Icon = feature.icon;
          return (
            <div
              key={feature.id}
              className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(feature.status)}`}>
                  {getStatusLabel(feature.status)}
                </span>
              </div>

              <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600 text-sm mb-4 line-clamp-3">{feature.description}</p>

              {/* Métriques */}
              <div className="grid grid-cols-3 gap-3 mb-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{feature.metrics.performance}%</div>
                  <div className="text-xs text-gray-500">Performance</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{feature.metrics.adoption}%</div>
                  <div className="text-xs text-gray-500">Adoption</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600">{feature.metrics.satisfaction}%</div>
                  <div className="text-xs text-gray-500">Satisfaction</div>
                </div>
              </div>

              {/* Technologies */}
              <div className="flex flex-wrap gap-1 mb-4">
                {feature.technologies.slice(0, 3).map((tech, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                  >
                    {tech}
                  </span>
                ))}
                {feature.technologies.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    +{feature.technologies.length - 3}
                  </span>
                )}
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => setSelectedFeature(feature)}
                  className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium"
                >
                  Détails
                </button>
                <button
                  onClick={() => runDemo(feature.id)}
                  disabled={isRunningDemo}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium disabled:opacity-50 flex items-center justify-center"
                >
                  {isRunningDemo ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-1" />
                      Démo
                    </>
                  )}
                </button>
              </div>

              {/* Résultats de démo */}
              {demoResults[feature.id] && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center mb-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span className="text-green-800 font-medium text-sm">Démo réussie</span>
                  </div>
                  <div className="text-xs text-green-700">
                    {feature.id === 'ai-scoring' && demoResults[feature.id].score && (
                      <div>Score calculé: {demoResults[feature.id].score}/100</div>
                    )}
                    {feature.id === 'chatbot-ai' && demoResults[feature.id].text && (
                      <div>Réponse: {demoResults[feature.id].text.substring(0, 50)}...</div>
                    )}
                    {feature.id === 'blockchain' && demoResults[feature.id].stats && (
                      <div>Blocs: {demoResults[feature.id].stats.totalBlocks}</div>
                    )}
                    {feature.id === 'document-ai' && demoResults[feature.id].fileName && (
                      <div>Document: {demoResults[feature.id].fileName}</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Modal de détails */}
      {selectedFeature && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <selectedFeature.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900">{selectedFeature.title}</h3>
                    <p className="text-gray-600">{selectedFeature.description}</p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedFeature(null)}
                  className="text-gray-400 hover:text-gray-600 p-2"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Avantages */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Award className="w-5 h-5 mr-2 text-yellow-500" />
                  Avantages Clés
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {selectedFeature.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Technologies */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Cpu className="w-5 h-5 mr-2 text-blue-500" />
                  Technologies Utilisées
                </h4>
                <div className="flex flex-wrap gap-2">
                  {selectedFeature.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Métriques détaillées */}
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-purple-500" />
                  Métriques de Performance
                </h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedFeature.metrics.performance}%</div>
                    <div className="text-sm text-blue-700">Performance</div>
                    <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${selectedFeature.metrics.performance}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{selectedFeature.metrics.adoption}%</div>
                    <div className="text-sm text-green-700">Adoption</div>
                    <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${selectedFeature.metrics.adoption}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-purple-600">{selectedFeature.metrics.satisfaction}%</div>
                    <div className="text-sm text-purple-700">Satisfaction</div>
                    <div className="w-full bg-purple-200 rounded-full h-2 mt-2">
                      <div 
                        className="bg-purple-600 h-2 rounded-full" 
                        style={{ width: `${selectedFeature.metrics.satisfaction}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Footer avec informations commerciales */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
        <h2 className="text-2xl font-bold mb-4">TuniBankX - L'Avenir de la Banque en Tunisie</h2>
        <p className="text-blue-100 mb-6 max-w-3xl mx-auto">
          Plateforme bancaire révolutionnaire combinant IA, blockchain et technologies de pointe 
          pour offrir une expérience bancaire unique et sécurisée aux institutions financières tunisiennes.
        </p>
        <div className="flex items-center justify-center space-x-8">
          <div className="flex items-center space-x-2">
            <Star className="w-5 h-5 text-yellow-400" />
            <span>Solution Unique en Tunisie</span>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="w-5 h-5 text-green-400" />
            <span>Sécurité Maximale</span>
          </div>
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-yellow-400" />
            <span>Performance Optimale</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechnologyShowcase;
