<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TuniBankX - Démonstration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3730a3 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .face-scanner {
            border: 2px solid #3b82f6;
            position: relative;
        }
        .face-scanner::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            width: 20px;
            height: 20px;
            border-top: 2px solid #3b82f6;
            border-left: 2px solid #3b82f6;
        }
        .face-scanner::after {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            border-top: 2px solid #3b82f6;
            border-right: 2px solid #3b82f6;
        }
        .scanning-animation {
            animation: scan 2s infinite;
        }
        @keyframes scan {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="min-h-screen gradient-bg">
    <!-- Page de Login -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center p-4">
        <div class="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>
        
        <div class="relative z-10 w-full max-w-md">
            <div class="glass-effect rounded-2xl shadow-2xl p-8">
                <!-- En-tête -->
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">TB</span>
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">TuniBankX</h1>
                    <p class="text-gray-600">Plateforme Bancaire Sécurisée</p>
                </div>

                <!-- Sélecteur de mode -->
                <div class="flex bg-gray-100 rounded-lg p-1 mb-6">
                    <button id="faceIdBtn" class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all bg-white text-blue-600 shadow-sm">
                        📷 Face ID
                    </button>
                    <button id="pinBtn" class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all text-gray-600 hover:text-gray-900">
                        🔒 PIN
                    </button>
                </div>

                <!-- Interface Face ID -->
                <div id="faceIdInterface" class="space-y-4">
                    <div id="cameraSection" class="text-center">
                        <div class="w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-4xl">📷</span>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Utilisez la reconnaissance faciale pour vous connecter de manière sécurisée
                        </p>
                        <button id="activateCameraBtn" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            📷 Activer la caméra
                        </button>
                    </div>

                    <div id="scanningSection" class="hidden space-y-4">
                        <div class="relative">
                            <div class="w-full h-48 bg-gray-900 rounded-lg face-scanner scanning-animation flex items-center justify-center">
                                <span class="text-white text-lg">👤 Positionnez votre visage</span>
                            </div>
                        </div>
                        <p id="scanStatus" class="text-center text-sm text-blue-600">Caméra activée. Positionnez votre visage dans le cadre.</p>
                        <div class="flex space-x-3">
                            <button id="scanBtn" class="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                👁️ Scanner
                            </button>
                            <button id="cancelBtn" class="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                Annuler
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Interface PIN -->
                <div id="pinInterface" class="hidden space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nom d'utilisateur</label>
                        <input type="text" id="username" value="directeur.credit" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Votre nom d'utilisateur">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Code PIN</label>
                        <input type="password" id="pinCode" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Votre code PIN">
                    </div>
                    <button id="loginBtn" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                        🔒 Se connecter
                    </button>
                </div>

                <!-- Messages -->
                <div id="messageArea" class="mt-4 hidden">
                    <div id="errorMessage" class="hidden p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
                        <span class="text-red-500 mr-2">⚠️</span>
                        <span class="text-red-700 text-sm" id="errorText"></span>
                    </div>
                    <div id="successMessage" class="hidden p-3 bg-green-50 border border-green-200 rounded-lg flex items-center">
                        <span class="text-green-500 mr-2">✅</span>
                        <span class="text-green-700 text-sm" id="successText"></span>
                    </div>
                </div>

                <!-- Informations de test -->
                <div class="mt-6 p-3 bg-blue-50 rounded-lg">
                    <p class="text-xs text-blue-600 text-center">
                        <strong>Compte de test :</strong><br />
                        Utilisateur: directeur.credit | PIN: 1234
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboardPage" class="hidden min-h-screen bg-gray-50">
        <!-- Header -->
        <header class="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">TB</span>
                    </div>
                    <h1 class="text-xl font-bold text-gray-900">TuniBankX</h1>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-sm font-medium text-gray-900">Directeur Crédit</span>
                <button id="logoutBtn" class="text-gray-400 hover:text-red-600 cursor-pointer transition-colors" title="Se déconnecter">
                    🚪
                </button>
            </div>
        </header>

        <!-- Contenu principal -->
        <div class="p-6 space-y-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Tableau de Bord</h2>
                <p class="text-gray-600">Vue d'ensemble de votre activité bancaire</p>
            </div>

            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Clients Actifs</p>
                            <p class="text-2xl font-bold text-gray-900">6</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span class="text-blue-600 text-xl">👥</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center">
                        <span class="text-green-600 text-sm font-medium">+12.5%</span>
                        <span class="text-gray-500 text-sm ml-2">vs mois dernier</span>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Crédits en Cours</p>
                            <p class="text-2xl font-bold text-gray-900">138,000 TND</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <span class="text-green-600 text-xl">💳</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center">
                        <span class="text-green-600 text-sm font-medium">+8.2%</span>
                        <span class="text-gray-500 text-sm ml-2">vs mois dernier</span>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Taux de Défaut</p>
                            <p class="text-2xl font-bold text-gray-900">12.5%</p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <span class="text-red-600 text-xl">⚠️</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center">
                        <span class="text-red-600 text-sm font-medium">-0.8%</span>
                        <span class="text-gray-500 text-sm ml-2">vs mois dernier</span>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Score Moyen</p>
                            <p class="text-2xl font-bold text-gray-900">71</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <span class="text-purple-600 text-xl">📊</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center">
                        <span class="text-green-600 text-sm font-medium">+2.1%</span>
                        <span class="text-gray-500 text-sm ml-2">vs mois dernier</span>
                    </div>
                </div>
            </div>

            <!-- Message de succès -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <span class="text-green-500 text-xl mr-3">🎉</span>
                    <div>
                        <h3 class="text-green-800 font-medium">Authentification réussie !</h3>
                        <p class="text-green-700 text-sm">Vous êtes maintenant connecté à TuniBankX avec Face ID. Toutes les fonctionnalités sont opérationnelles.</p>
                    </div>
                </div>
            </div>

            <!-- Fonctionnalités disponibles -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Fonctionnalités Disponibles</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="text-blue-600 text-2xl mb-2">🔐</div>
                        <h4 class="font-medium text-gray-900">Authentification Face ID</h4>
                        <p class="text-sm text-gray-600">Connexion sécurisée par reconnaissance faciale</p>
                    </div>
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="text-green-600 text-2xl mb-2">💾</div>
                        <h4 class="font-medium text-gray-900">Base de Données</h4>
                        <p class="text-sm text-gray-600">8 clients de test avec données réalistes</p>
                    </div>
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="text-purple-600 text-2xl mb-2">📊</div>
                        <h4 class="font-medium text-gray-900">Dashboard Interactif</h4>
                        <p class="text-sm text-gray-600">Statistiques en temps réel</p>
                    </div>
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="text-orange-600 text-2xl mb-2">🤖</div>
                        <h4 class="font-medium text-gray-900">Scoring IA</h4>
                        <p class="text-sm text-gray-600">Analyse prédictive des risques</p>
                    </div>
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="text-red-600 text-2xl mb-2">📄</div>
                        <h4 class="font-medium text-gray-900">Génération Documents</h4>
                        <p class="text-sm text-gray-600">Contrats automatisés</p>
                    </div>
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <div class="text-indigo-600 text-2xl mb-2">💬</div>
                        <h4 class="font-medium text-gray-900">Chatbot IA</h4>
                        <p class="text-sm text-gray-600">Assistant intelligent intégré</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulation de l'authentification
        const loginPage = document.getElementById('loginPage');
        const dashboardPage = document.getElementById('dashboardPage');
        const faceIdBtn = document.getElementById('faceIdBtn');
        const pinBtn = document.getElementById('pinBtn');
        const faceIdInterface = document.getElementById('faceIdInterface');
        const pinInterface = document.getElementById('pinInterface');
        const activateCameraBtn = document.getElementById('activateCameraBtn');
        const cameraSection = document.getElementById('cameraSection');
        const scanningSection = document.getElementById('scanningSection');
        const scanBtn = document.getElementById('scanBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const messageArea = document.getElementById('messageArea');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const scanStatus = document.getElementById('scanStatus');

        // Gestion des onglets
        faceIdBtn.addEventListener('click', () => {
            faceIdBtn.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all bg-white text-blue-600 shadow-sm';
            pinBtn.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all text-gray-600 hover:text-gray-900';
            faceIdInterface.classList.remove('hidden');
            pinInterface.classList.add('hidden');
            hideMessages();
        });

        pinBtn.addEventListener('click', () => {
            pinBtn.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all bg-white text-blue-600 shadow-sm';
            faceIdBtn.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all text-gray-600 hover:text-gray-900';
            pinInterface.classList.remove('hidden');
            faceIdInterface.classList.add('hidden');
            hideMessages();
        });

        // Activation de la caméra
        activateCameraBtn.addEventListener('click', () => {
            cameraSection.classList.add('hidden');
            scanningSection.classList.remove('hidden');
        });

        // Annulation
        cancelBtn.addEventListener('click', () => {
            scanningSection.classList.add('hidden');
            cameraSection.classList.remove('hidden');
        });

        // Scan Face ID
        scanBtn.addEventListener('click', () => {
            scanBtn.disabled = true;
            scanBtn.innerHTML = '⏳ Analyse en cours...';
            scanStatus.textContent = 'Analyse du visage en cours...';
            
            setTimeout(() => {
                scanStatus.textContent = 'Vérification de l\'identité...';
                setTimeout(() => {
                    showSuccess('Authentification réussie !');
                    setTimeout(() => {
                        showDashboard();
                    }, 1500);
                }, 1000);
            }, 2000);
        });

        // Login PIN
        loginBtn.addEventListener('click', () => {
            const username = document.getElementById('username').value;
            const pin = document.getElementById('pinCode').value;
            
            if (username === 'directeur.credit' && pin === '1234') {
                showSuccess('Connexion réussie !');
                setTimeout(() => {
                    showDashboard();
                }, 1500);
            } else {
                showError('Nom d\'utilisateur ou PIN incorrect');
            }
        });

        // Déconnexion
        logoutBtn.addEventListener('click', () => {
            dashboardPage.classList.add('hidden');
            loginPage.classList.remove('hidden');
            resetLogin();
        });

        // Fonctions utilitaires
        function showError(message) {
            messageArea.classList.remove('hidden');
            errorMessage.classList.remove('hidden');
            successMessage.classList.add('hidden');
            document.getElementById('errorText').textContent = message;
        }

        function showSuccess(message) {
            messageArea.classList.remove('hidden');
            successMessage.classList.remove('hidden');
            errorMessage.classList.add('hidden');
            document.getElementById('successText').textContent = message;
        }

        function hideMessages() {
            messageArea.classList.add('hidden');
            errorMessage.classList.add('hidden');
            successMessage.classList.add('hidden');
        }

        function showDashboard() {
            loginPage.classList.add('hidden');
            dashboardPage.classList.remove('hidden');
        }

        function resetLogin() {
            scanningSection.classList.add('hidden');
            cameraSection.classList.remove('hidden');
            scanBtn.disabled = false;
            scanBtn.innerHTML = '👁️ Scanner';
            scanStatus.textContent = 'Caméra activée. Positionnez votre visage dans le cadre.';
            document.getElementById('pinCode').value = '';
            hideMessages();
        }

        // Gestion du clavier pour le PIN
        document.getElementById('pinCode').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                loginBtn.click();
            }
        });
    </script>
</body>
</html>
