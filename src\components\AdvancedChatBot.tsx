import React, { useState, useEffect, useRef } from 'react';
import { 
  MessageCircle, X, Send, Mic, Globe, Volume2, VolumeX, 
  Settings, Zap, Brain, Languages, Sparkles, MicOff, Loader2,
  User, Bot, Clock, CheckCircle, AlertCircle
} from 'lucide-react';
import { AIChatbotService, ChatMessage, BotResponse } from '../services/aiChatbotService';

interface QuickReply {
  text: string;
  action?: string;
}

const AdvancedChatBot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<'fr' | 'ar' | 'en'>('fr');
  const [quickReplies, setQuickReplies] = useState<QuickReply[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const chatService = AIChatbotService.getInstance();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const recognition = useRef<any>(null);

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      initializeChat();
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    initializeSpeechRecognition();
  }, []);

  const initializeChat = async () => {
    const welcomeMessage = await getWelcomeMessage();
    const botMessage: ChatMessage = {
      id: 'welcome',
      text: welcomeMessage.text,
      sender: 'bot',
      timestamp: new Date(),
      language: currentLanguage
    };
    
    setMessages([botMessage]);
    setQuickReplies(welcomeMessage.quickReplies || []);
  };

  const getWelcomeMessage = async (): Promise<BotResponse> => {
    const welcomeMessages = {
      fr: "🌟 Bonjour ! Je suis votre assistant IA TuniBankX. Je parle français, arabe et anglais. Comment puis-je vous aider aujourd'hui ?",
      en: "🌟 Hello! I'm your TuniBankX AI assistant. I speak French, Arabic and English. How can I help you today?",
      ar: "🌟 مرحبا! أنا مساعدك الذكي في تونيبانك إكس. أتحدث الفرنسية والعربية والإنجليزية. كيف يمكنني مساعدتك اليوم؟"
    };

    return {
      text: welcomeMessages[currentLanguage],
      quickReplies: getInitialQuickReplies(),
      confidence: 1.0,
      language: currentLanguage,
      actions: []
    };
  };

  const getInitialQuickReplies = (): string[] => {
    const replies = {
      fr: ['Consulter mon solde', 'Demander un crédit', 'Problème de carte', 'Prendre RDV'],
      en: ['Check balance', 'Apply for credit', 'Card issue', 'Book appointment'],
      ar: ['استعلام الرصيد', 'طلب قرض', 'مشكلة بطاقة', 'حجز موعد']
    };
    return replies[currentLanguage];
  };

  const handleSendMessage = async (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText) return;

    setIsProcessing(true);
    setInputText('');

    // Ajouter le message utilisateur
    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      text: messageText,
      sender: 'user',
      timestamp: new Date(),
      language: currentLanguage
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // Traitement par l'IA
      const response = await chatService.processMessage(messageText, 'user123', currentLanguage);
      
      // Simulation d'un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      // Ajouter la réponse du bot
      const botMessage: ChatMessage = {
        id: `bot_${Date.now()}`,
        text: response.text,
        sender: 'bot',
        timestamp: new Date(),
        language: response.language,
        intent: 'response',
        confidence: response.confidence
      };

      setMessages(prev => [...prev, botMessage]);
      setQuickReplies(response.quickReplies || []);

      // Lecture vocale si activée
      if (voiceEnabled) {
        speakText(response.text, response.language);
      }

      // Actions spéciales
      if (response.actions) {
        handleBotActions(response.actions);
      }

    } catch (error) {
      console.error('Erreur lors du traitement du message:', error);
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        text: getErrorMessage(),
        sender: 'bot',
        timestamp: new Date(),
        language: currentLanguage
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      setIsProcessing(false);
    }
  };

  const getErrorMessage = (): string => {
    const errorMessages = {
      fr: "Désolé, j'ai rencontré un problème technique. Pouvez-vous réessayer ?",
      en: "Sorry, I encountered a technical issue. Can you try again?",
      ar: "عذرا، واجهت مشكلة تقنية. هل يمكنك المحاولة مرة أخرى؟"
    };
    return errorMessages[currentLanguage];
  };

  const handleBotActions = (actions: string[]) => {
    actions.forEach(action => {
      switch (action) {
        case 'show_balance':
          // Redirection vers la page de solde
          break;
        case 'start_credit_application':
          // Redirection vers demande de crédit
          break;
        case 'contact_support':
          // Contact support humain
          break;
      }
    });
  };

  const initializeSpeechRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      recognition.current = new SpeechRecognition();
      
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = getLanguageCode(currentLanguage);

      recognition.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setInputText(transcript);
        setIsListening(false);
      };

      recognition.current.onerror = () => {
        setIsListening(false);
      };

      recognition.current.onend = () => {
        setIsListening(false);
      };
    }
  };

  const getLanguageCode = (lang: 'fr' | 'ar' | 'en'): string => {
    const codes = { fr: 'fr-FR', ar: 'ar-TN', en: 'en-US' };
    return codes[lang];
  };

  const toggleVoiceRecognition = () => {
    if (isListening) {
      recognition.current?.stop();
      setIsListening(false);
    } else {
      recognition.current?.start();
      setIsListening(true);
    }
  };

  const speakText = (text: string, language: 'fr' | 'ar' | 'en') => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = getLanguageCode(language);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    }
  };

  const changeLanguage = (newLanguage: 'fr' | 'ar' | 'en') => {
    setCurrentLanguage(newLanguage);
    if (recognition.current) {
      recognition.current.lang = getLanguageCode(newLanguage);
    }
    // Recharger le message de bienvenue
    initializeChat();
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  };

  const getLanguageFlag = (lang: 'fr' | 'ar' | 'en'): string => {
    const flags = { fr: '🇫🇷', ar: '🇹🇳', en: '🇺🇸' };
    return flags[lang];
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 group"
        >
          <div className="relative">
            <MessageCircle className="w-6 h-6" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          </div>
          <div className="absolute -top-12 right-0 bg-gray-900 text-white px-3 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Assistant IA TuniBankX
          </div>
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50 w-96 h-[600px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col overflow-hidden">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
            <Brain className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-semibold">Assistant IA TuniBankX</h3>
            <div className="flex items-center space-x-1 text-xs text-blue-100">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>En ligne • Multilingue</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <Settings className="w-4 h-4" />
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Paramètres */}
      {showSettings && (
        <div className="bg-gray-50 p-4 border-b border-gray-200">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Langue</label>
              <div className="flex space-x-2">
                {(['fr', 'ar', 'en'] as const).map(lang => (
                  <button
                    key={lang}
                    onClick={() => changeLanguage(lang)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                      currentLanguage === lang
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {getLanguageFlag(lang)} {lang.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Synthèse vocale</span>
              <button
                onClick={() => setVoiceEnabled(!voiceEnabled)}
                className={`p-2 rounded-lg transition-colors ${
                  voiceEnabled ? 'text-blue-600 bg-blue-100' : 'text-gray-400 bg-gray-100'
                }`}
              >
                {voiceEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`max-w-[80%] ${message.sender === 'user' ? 'order-2' : 'order-1'}`}>
              <div
                className={`p-3 rounded-2xl ${
                  message.sender === 'user'
                    ? 'bg-blue-600 text-white rounded-br-md'
                    : 'bg-gray-100 text-gray-900 rounded-bl-md'
                }`}
              >
                <p className="text-sm">{message.text}</p>
                {message.confidence && message.confidence < 0.8 && (
                  <div className="flex items-center mt-2 text-xs text-yellow-600">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Confiance: {Math.round(message.confidence * 100)}%
                  </div>
                )}
              </div>
              <div className={`flex items-center mt-1 text-xs text-gray-500 ${
                message.sender === 'user' ? 'justify-end' : 'justify-start'
              }`}>
                {message.sender === 'user' ? <User className="w-3 h-3 mr-1" /> : <Bot className="w-3 h-3 mr-1" />}
                <span>{formatTime(message.timestamp)}</span>
                {message.language && (
                  <span className="ml-2">{getLanguageFlag(message.language)}</span>
                )}
              </div>
            </div>
          </div>
        ))}

        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 p-3 rounded-2xl rounded-bl-md">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Réponses rapides */}
      {quickReplies.length > 0 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-2">
            {quickReplies.map((reply, index) => (
              <button
                key={index}
                onClick={() => handleSendMessage(reply)}
                className="px-3 py-1 bg-white border border-gray-300 rounded-full text-sm text-gray-700 hover:bg-blue-50 hover:border-blue-300 transition-colors"
              >
                {reply}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Zone de saisie */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && !isProcessing && handleSendMessage()}
              placeholder={
                currentLanguage === 'fr' ? 'Tapez votre message...' :
                currentLanguage === 'en' ? 'Type your message...' :
                'اكتب رسالتك...'
              }
              className="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isProcessing}
              dir={currentLanguage === 'ar' ? 'rtl' : 'ltr'}
            />
            {isProcessing && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
              </div>
            )}
          </div>
          
          {recognition.current && (
            <button
              onClick={toggleVoiceRecognition}
              className={`p-2 rounded-full transition-colors ${
                isListening 
                  ? 'bg-red-100 text-red-600 animate-pulse' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              disabled={isProcessing}
            >
              {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
            </button>
          )}
          
          <button
            onClick={() => handleSendMessage()}
            disabled={!inputText.trim() || isProcessing}
            className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
        
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-3 h-3" />
            <span>Propulsé par l'IA TuniBankX</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span>Sécurisé</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedChatBot;
