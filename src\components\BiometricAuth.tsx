import React, { useState, useEffect, useRef } from 'react';
import { Camera, User, Shield, CheckCircle, XCircle, Loader, Eye, Fingerprint, Mic } from 'lucide-react';

interface BiometricAuthProps {
  onAuthSuccess: (userData: any) => void;
  onAuthFailed: () => void;
}

const BiometricAuth: React.FC<BiometricAuthProps> = ({ onAuthSuccess, onAuthFailed }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [authStep, setAuthStep] = useState<'camera' | 'processing' | 'success' | 'failed' | 'voice'>('camera');
  const [authMode, setAuthMode] = useState<'face' | 'voice' | 'fingerprint'>('face');
  const [cameraStream, setCameraStream] = useState<MediaStream | null>(null);
  const [authProgress, setAuthProgress] = useState(0);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [voiceProgress, setVoiceProgress] = useState(0);
  const [detectedFeatures, setDetectedFeatures] = useState({
    faceDetected: false,
    eyesDetected: false,
    qualityGood: false,
    livenessConfirmed: false
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Démarrer la caméra automatiquement
  useEffect(() => {
    startCamera();
    return () => {
      stopCamera();
    };
  }, []);

  const startCamera = async () => {
    try {
      setCameraError(null);
      setIsLoading(true);

      // Vérifier si les médias sont supportés
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Votre navigateur ne supporte pas l\'accès à la caméra');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          facingMode: 'user'
        },
        audio: false
      });

      setCameraStream(stream);
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();
      }

      setIsLoading(false);
      setCameraError(null);

      // Démarrer la détection automatique après 2 secondes
      setTimeout(() => {
        if (authMode === 'face') {
          startFaceDetection();
        }
      }, 2000);

    } catch (error: any) {
      console.error('Erreur accès caméra:', error);
      setIsLoading(false);

      let errorMessage = 'Erreur d\'accès à la caméra';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Accès à la caméra refusé. Veuillez autoriser l\'accès dans votre navigateur.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'Aucune caméra trouvée sur cet appareil.';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Caméra déjà utilisée par une autre application.';
      }

      setCameraError(errorMessage);
      setAuthStep('failed');
    }
  };

  const stopCamera = () => {
    if (cameraStream) {
      cameraStream.getTracks().forEach(track => track.stop());
      setCameraStream(null);
    }
  };

  const startFaceDetection = () => {
    setAuthStep('processing');
    
    // Simulation de la détection biométrique progressive
    const detectionSteps = [
      { step: 'faceDetected', delay: 500, message: 'Visage détecté' },
      { step: 'eyesDetected', delay: 1000, message: 'Yeux détectés' },
      { step: 'qualityGood', delay: 1500, message: 'Qualité image validée' },
      { step: 'livenessConfirmed', delay: 2000, message: 'Vivacité confirmée' }
    ];

    detectionSteps.forEach(({ step, delay }) => {
      setTimeout(() => {
        setDetectedFeatures(prev => ({ ...prev, [step]: true }));
        setAuthProgress(prev => prev + 25);
      }, delay);
    });

    // Finaliser l'authentification
    setTimeout(() => {
      captureAndAuthenticate();
    }, 2500);
  };

  const captureAndAuthenticate = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx.drawImage(video, 0, 0);
        
        // Simulation de l'authentification réussie
        setTimeout(() => {
          setAuthStep('success');
          setTimeout(() => {
            stopCamera();
            onAuthSuccess({
              id: 1,
              nom: 'Ben Ali',
              prenom: 'Ahmed',
              email: '<EMAIL>',
              role: 'Directeur Crédit & IA',
              authMethod: 'biometric_face',
              authTime: new Date().toISOString(),
              biometricScore: 98.7
            });
          }, 1500);
        }, 1000);
      }
    }
  };

  const retryAuthentication = () => {
    if (authMode === 'face') {
      setAuthStep('camera');
      setAuthProgress(0);
      setDetectedFeatures({
        faceDetected: false,
        eyesDetected: false,
        qualityGood: false,
        livenessConfirmed: false
      });
      setCameraError(null);
      setTimeout(() => startFaceDetection(), 1000);
    } else if (authMode === 'voice') {
      setAuthStep('voice');
      setVoiceProgress(0);
      setIsRecording(false);
    }
  };

  // Fonctions d'authentification vocale
  const startVoiceAuth = async () => {
    try {
      setAuthMode('voice');
      setAuthStep('voice');
      setIsRecording(false);
      setVoiceProgress(0);

      // Arrêter la caméra si elle est active
      if (cameraStream) {
        stopCamera();
      }
    } catch (error) {
      console.error('Erreur initialisation voix:', error);
    }
  };

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        processVoiceAuth();
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);

      // Simulation de progression d'enregistrement
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setVoiceProgress(progress);
        if (progress >= 100) {
          clearInterval(interval);
          stopVoiceRecording();
        }
      }, 300);

    } catch (error) {
      console.error('Erreur enregistrement vocal:', error);
      alert('Erreur d\'accès au microphone. Veuillez autoriser l\'accès.');
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const processVoiceAuth = () => {
    // Simulation de traitement vocal
    setTimeout(() => {
      setAuthStep('success');
      setTimeout(() => {
        onAuthSuccess({
          id: 1,
          nom: 'Ben Ali',
          prenom: 'Ahmed',
          email: '<EMAIL>',
          role: 'Directeur Crédit & IA',
          authMethod: 'biometric_voice',
          authTime: new Date().toISOString(),
          biometricScore: 96.3
        });
      }, 1500);
    }, 1000);
  };

  // Fonction pour basculer vers empreinte
  const startFingerprintAuth = () => {
    setAuthMode('fingerprint');
    alert('Authentification par empreinte digitale non disponible sur ce navigateur. Utilisez Face ID ou reconnaissance vocale.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Shield className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">TuniBankX Enterprise</h1>
          <p className="text-xl text-blue-200">Authentification Biométrique Avancée</p>
          <p className="text-blue-300 mt-2">Technologie Face ID de niveau banque mondiale</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Caméra et Détection */}
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Reconnaissance Faciale</h2>
              <p className="text-blue-200">Positionnez votre visage dans le cadre</p>
            </div>

            {/* Zone Caméra ou Voix */}
            <div className="relative bg-black rounded-xl overflow-hidden mb-6" style={{ aspectRatio: '16/9' }}>
              {isLoading ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <Loader className="w-8 h-8 text-blue-400 animate-spin mx-auto mb-4" />
                    <p className="text-blue-200">
                      {authMode === 'face' ? 'Initialisation de la caméra...' : 'Initialisation du microphone...'}
                    </p>
                  </div>
                </div>
              ) : cameraError ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center p-6">
                    <XCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                    <p className="text-red-300 mb-4">{cameraError}</p>
                    <div className="space-y-3">
                      <button
                        onClick={startCamera}
                        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mx-auto"
                      >
                        <Camera className="w-4 h-4" />
                        <span>Réessayer Caméra</span>
                      </button>
                      <button
                        onClick={startVoiceAuth}
                        className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 mx-auto"
                      >
                        <Mic className="w-4 h-4" />
                        <span>Utiliser la Voix</span>
                      </button>
                    </div>
                  </div>
                </div>
              ) : authMode === 'voice' ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center p-6">
                    <div className={`w-32 h-32 rounded-full border-4 flex items-center justify-center mx-auto mb-6 transition-all duration-300 ${
                      isRecording ? 'border-red-500 bg-red-500/20 animate-pulse' : 'border-purple-500 bg-purple-500/20'
                    }`}>
                      <Mic className={`w-16 h-16 ${isRecording ? 'text-red-400' : 'text-purple-400'}`} />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">Authentification Vocale</h3>
                    <p className="text-purple-200 mb-4">
                      {!isRecording ? 'Cliquez pour commencer l\'enregistrement' : 'Parlez maintenant...'}
                    </p>
                    {isRecording && (
                      <div className="mb-4">
                        <div className="w-full bg-white/20 rounded-full h-3 mb-2">
                          <div
                            className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-300"
                            style={{ width: `${voiceProgress}%` }}
                          ></div>
                        </div>
                        <p className="text-purple-300 text-sm">{voiceProgress}% - Dites: "Je suis Ahmed Ben Ali"</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <>
                  <video
                    ref={videoRef}
                    className="w-full h-full object-cover"
                    autoPlay
                    muted
                    playsInline
                  />

                  {/* Overlay de détection */}
                  <div className="absolute inset-0">
                    {/* Cadre de détection */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className={`w-64 h-80 border-4 rounded-2xl transition-colors duration-500 ${
                        authStep === 'processing' ? 'border-yellow-400' :
                        authStep === 'success' ? 'border-green-400' :
                        authStep === 'failed' ? 'border-red-400' :
                        'border-blue-400'
                      }`}>
                        {/* Coins du cadre */}
                        <div className="absolute -top-1 -left-1 w-8 h-8 border-l-4 border-t-4 border-white rounded-tl-lg"></div>
                        <div className="absolute -top-1 -right-1 w-8 h-8 border-r-4 border-t-4 border-white rounded-tr-lg"></div>
                        <div className="absolute -bottom-1 -left-1 w-8 h-8 border-l-4 border-b-4 border-white rounded-bl-lg"></div>
                        <div className="absolute -bottom-1 -right-1 w-8 h-8 border-r-4 border-b-4 border-white rounded-br-lg"></div>
                      </div>
                    </div>

                    {/* Instructions */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2">
                        <p className="text-white text-sm text-center">
                          {authStep === 'camera' && 'Regardez directement la caméra'}
                          {authStep === 'processing' && 'Analyse en cours...'}
                          {authStep === 'success' && 'Authentification réussie !'}
                          {authStep === 'failed' && 'Échec de l\'authentification'}
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Canvas caché pour capture */}
            <canvas ref={canvasRef} className="hidden" />

            {/* Boutons d'action */}
            <div className="flex justify-center space-x-4 flex-wrap gap-2">
              {authStep === 'camera' && !isLoading && !cameraError && authMode === 'face' && (
                <button
                  onClick={startFaceDetection}
                  className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Camera className="w-5 h-5" />
                  <span>Démarrer Scan Facial</span>
                </button>
              )}

              {authStep === 'voice' && !isRecording && authMode === 'voice' && (
                <button
                  onClick={startVoiceRecording}
                  className="flex items-center space-x-2 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Mic className="w-5 h-5" />
                  <span>Commencer Enregistrement</span>
                </button>
              )}

              {isRecording && (
                <button
                  onClick={stopVoiceRecording}
                  className="flex items-center space-x-2 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors animate-pulse"
                >
                  <XCircle className="w-5 h-5" />
                  <span>Arrêter</span>
                </button>
              )}

              {authStep === 'failed' && (
                <button
                  onClick={retryAuthentication}
                  className="flex items-center space-x-2 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <Camera className="w-5 h-5" />
                  <span>Réessayer</span>
                </button>
              )}

              {/* Boutons de basculement de méthode */}
              {!isLoading && authStep !== 'processing' && authStep !== 'success' && (
                <div className="flex space-x-2">
                  {authMode !== 'face' && (
                    <button
                      onClick={() => { setAuthMode('face'); setAuthStep('camera'); startCamera(); }}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-500/20 text-blue-300 border border-blue-500 rounded-lg hover:bg-blue-500/30 transition-colors"
                    >
                      <Camera className="w-4 h-4" />
                      <span>Face ID</span>
                    </button>
                  )}

                  {authMode !== 'voice' && (
                    <button
                      onClick={startVoiceAuth}
                      className="flex items-center space-x-2 px-4 py-2 bg-purple-500/20 text-purple-300 border border-purple-500 rounded-lg hover:bg-purple-500/30 transition-colors"
                    >
                      <Mic className="w-4 h-4" />
                      <span>Voix</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
          {/* Panneau de Status */}
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6">Status d'Authentification</h3>

            {/* Barre de progression */}
            {authStep === 'processing' && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-blue-200">Progression</span>
                  <span className="text-white font-semibold">{authProgress}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${authProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Étapes de détection */}
            <div className="space-y-4 mb-6">
              {[
                { key: 'faceDetected', label: 'Détection du visage', icon: User },
                { key: 'eyesDetected', label: 'Détection des yeux', icon: Eye },
                { key: 'qualityGood', label: 'Qualité de l\'image', icon: Camera },
                { key: 'livenessConfirmed', label: 'Test de vivacité', icon: CheckCircle }
              ].map(({ key, label, icon: Icon }) => (
                <div key={key} className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    detectedFeatures[key as keyof typeof detectedFeatures]
                      ? 'bg-green-500'
                      : 'bg-white/20'
                  }`}>
                    {detectedFeatures[key as keyof typeof detectedFeatures] ? (
                      <CheckCircle className="w-5 h-5 text-white" />
                    ) : (
                      <Icon className="w-5 h-5 text-white/60" />
                    )}
                  </div>
                  <span className={`${
                    detectedFeatures[key as keyof typeof detectedFeatures]
                      ? 'text-green-300'
                      : 'text-white/60'
                  }`}>
                    {label}
                  </span>
                </div>
              ))}
            </div>

            {/* Méthodes d'authentification alternatives */}
            <div className="border-t border-white/20 pt-6">
              <h4 className="text-white font-semibold mb-4">Méthodes Disponibles</h4>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => { setAuthMode('face'); setAuthStep('camera'); startCamera(); }}
                  className={`flex items-center space-x-2 p-3 rounded-lg transition-colors ${
                    authMode === 'face'
                      ? 'bg-blue-500/30 border border-blue-400'
                      : 'bg-white/10 hover:bg-white/20'
                  }`}
                >
                  <Camera className="w-5 h-5 text-blue-400" />
                  <span className="text-white text-sm">Face ID</span>
                </button>
                <button
                  onClick={startVoiceAuth}
                  className={`flex items-center space-x-2 p-3 rounded-lg transition-colors ${
                    authMode === 'voice'
                      ? 'bg-purple-500/30 border border-purple-400'
                      : 'bg-white/10 hover:bg-white/20'
                  }`}
                >
                  <Mic className="w-5 h-5 text-purple-400" />
                  <span className="text-white text-sm">Reconnaissance Vocale</span>
                </button>
                <button
                  onClick={startFingerprintAuth}
                  className="flex items-center space-x-2 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors opacity-50"
                >
                  <Fingerprint className="w-5 h-5 text-green-400" />
                  <span className="text-white text-sm">Empreinte (Bientôt)</span>
                </button>
                <button
                  onClick={() => onAuthFailed()}
                  className="flex items-center space-x-2 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
                >
                  <User className="w-5 h-5 text-gray-400" />
                  <span className="text-white text-sm">Login Traditionnel</span>
                </button>
              </div>
            </div>

            {/* Informations de sécurité */}
            <div className="mt-6 p-4 bg-blue-500/20 rounded-lg">
              <h5 className="text-blue-200 font-semibold mb-2">🔒 Sécurité Avancée</h5>
              <ul className="text-blue-300 text-sm space-y-1">
                <li>• Chiffrement AES-256</li>
                <li>• Détection anti-spoofing</li>
                <li>• Conformité RGPD</li>
                <li>• Audit trail complet</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-blue-300 text-sm">
            Technologie biométrique de niveau banque mondiale • Sécurisé par TuniBankX Enterprise
          </p>
        </div>
      </div>
    </div>
  );
};

export default BiometricAuth;
