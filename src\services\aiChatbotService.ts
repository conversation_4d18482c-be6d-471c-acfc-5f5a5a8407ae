// Service de Chatbot IA Multilingue Avancé pour TuniBankX
// Assistant virtuel intelligent avec NLP et compréhension contextuelle

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  language: 'fr' | 'ar' | 'en';
  intent?: string;
  confidence?: number;
  entities?: { [key: string]: any };
  attachments?: string[];
}

export interface ChatContext {
  userId: string;
  sessionId: string;
  currentLanguage: 'fr' | 'ar' | 'en';
  userProfile?: any;
  conversationHistory: ChatMessage[];
  currentIntent?: string;
  entities: { [key: string]: any };
  mood: 'positive' | 'neutral' | 'negative';
}

export interface BotResponse {
  text: string;
  quickReplies?: string[];
  actions?: string[];
  cards?: any[];
  needsHumanAgent?: boolean;
  confidence: number;
  language: 'fr' | 'ar' | 'en';
}

export class AIChatbotService {
  private static instance: AIChatbotService;
  private intents: { [key: string]: any };
  private entities: { [key: string]: string[] };
  private responses: { [key: string]: { [lang: string]: string[] } };
  private contexts: Map<string, ChatContext> = new Map();

  constructor() {
    this.initializeNLP();
    this.loadBankingKnowledge();
  }

  static getInstance(): AIChatbotService {
    if (!AIChatbotService.instance) {
      AIChatbotService.instance = new AIChatbotService();
    }
    return AIChatbotService.instance;
  }

  // Traitement principal du message
  async processMessage(
    message: string, 
    userId: string, 
    language: 'fr' | 'ar' | 'en' = 'fr'
  ): Promise<BotResponse> {
    const context = this.getOrCreateContext(userId, language);
    
    // Détection de la langue si non spécifiée
    const detectedLanguage = this.detectLanguage(message);
    if (detectedLanguage !== language) {
      context.currentLanguage = detectedLanguage;
    }

    // Analyse NLP du message
    const nlpResult = await this.analyzeMessage(message, context);
    
    // Mise à jour du contexte
    this.updateContext(context, message, nlpResult);
    
    // Génération de la réponse
    const response = await this.generateResponse(nlpResult, context);
    
    // Sauvegarde de l'historique
    this.saveToHistory(context, message, response);
    
    return response;
  }

  // Détection automatique de la langue
  private detectLanguage(text: string): 'fr' | 'ar' | 'en' {
    // Patterns pour détecter l'arabe
    const arabicPattern = /[\u0600-\u06FF]/;
    if (arabicPattern.test(text)) return 'ar';
    
    // Mots clés français
    const frenchKeywords = ['bonjour', 'salut', 'merci', 'crédit', 'compte', 'banque', 'argent'];
    const frenchCount = frenchKeywords.filter(word => 
      text.toLowerCase().includes(word)
    ).length;
    
    // Mots clés anglais
    const englishKeywords = ['hello', 'hi', 'thank', 'credit', 'account', 'bank', 'money'];
    const englishCount = englishKeywords.filter(word => 
      text.toLowerCase().includes(word)
    ).length;
    
    return frenchCount > englishCount ? 'fr' : 'en';
  }

  // Analyse NLP avancée
  private async analyzeMessage(message: string, context: ChatContext): Promise<any> {
    const normalizedMessage = this.normalizeText(message);
    
    // Détection d'intention
    const intent = this.detectIntent(normalizedMessage, context.currentLanguage);
    
    // Extraction d'entités
    const entities = this.extractEntities(normalizedMessage, context.currentLanguage);
    
    // Analyse de sentiment
    const sentiment = this.analyzeSentiment(normalizedMessage, context.currentLanguage);
    
    // Détection d'urgence
    const urgency = this.detectUrgency(normalizedMessage, context.currentLanguage);
    
    return {
      intent,
      entities,
      sentiment,
      urgency,
      confidence: intent.confidence
    };
  }

  // Détection d'intention multilingue
  private detectIntent(message: string, language: 'fr' | 'ar' | 'en'): any {
    const intents = {
      fr: {
        'salutation': ['bonjour', 'salut', 'bonsoir', 'hello'],
        'demande_solde': ['solde', 'combien', 'argent', 'compte'],
        'demande_credit': ['crédit', 'prêt', 'emprunt', 'financement'],
        'probleme_carte': ['carte', 'bloquée', 'problème', 'panne'],
        'virement': ['virement', 'transfert', 'envoyer', 'transférer'],
        'rdv': ['rendez-vous', 'rdv', 'rencontrer', 'agence'],
        'reclamation': ['problème', 'réclamation', 'plainte', 'erreur'],
        'information': ['comment', 'pourquoi', 'quand', 'où', 'info'],
        'remerciement': ['merci', 'thanks', 'شكرا'],
        'au_revoir': ['au revoir', 'bye', 'tchao', 'à bientôt']
      },
      en: {
        'greeting': ['hello', 'hi', 'good morning', 'good evening'],
        'balance_inquiry': ['balance', 'money', 'account', 'how much'],
        'credit_request': ['credit', 'loan', 'borrow', 'financing'],
        'card_issue': ['card', 'blocked', 'problem', 'issue'],
        'transfer': ['transfer', 'send', 'wire', 'payment'],
        'appointment': ['appointment', 'meet', 'branch', 'visit'],
        'complaint': ['problem', 'complaint', 'issue', 'error'],
        'information': ['how', 'why', 'when', 'where', 'info'],
        'thanks': ['thank', 'thanks', 'appreciate'],
        'goodbye': ['goodbye', 'bye', 'see you', 'farewell']
      },
      ar: {
        'تحية': ['مرحبا', 'أهلا', 'السلام عليكم', 'صباح الخير'],
        'استعلام_رصيد': ['رصيد', 'حساب', 'أموال', 'كم'],
        'طلب_قرض': ['قرض', 'تمويل', 'استدانة', 'ائتمان'],
        'مشكلة_بطاقة': ['بطاقة', 'مشكلة', 'عطل', 'محجوبة'],
        'تحويل': ['تحويل', 'إرسال', 'نقل', 'دفع'],
        'موعد': ['موعد', 'لقاء', 'فرع', 'زيارة'],
        'شكوى': ['مشكلة', 'شكوى', 'خطأ', 'اعتراض'],
        'معلومات': ['كيف', 'لماذا', 'متى', 'أين', 'معلومات'],
        'شكر': ['شكرا', 'أشكرك', 'ممتن'],
        'وداع': ['وداعا', 'إلى اللقاء', 'مع السلامة']
      }
    };

    let bestMatch = { intent: 'unknown', confidence: 0 };
    
    for (const [intentName, keywords] of Object.entries(intents[language])) {
      const matches = keywords.filter(keyword => 
        message.toLowerCase().includes(keyword.toLowerCase())
      );
      
      if (matches.length > 0) {
        const confidence = matches.length / keywords.length;
        if (confidence > bestMatch.confidence) {
          bestMatch = { intent: intentName, confidence };
        }
      }
    }
    
    return bestMatch;
  }

  // Extraction d'entités
  private extractEntities(message: string, language: 'fr' | 'ar' | 'en'): any {
    const entities: any = {};
    
    // Montants
    const amountRegex = /(\d+(?:[.,]\d+)?)\s*(tnd|dinars?|dt|€|euros?|\$|dollars?)/gi;
    const amountMatches = message.match(amountRegex);
    if (amountMatches) {
      entities.amount = amountMatches[0];
    }
    
    // Dates
    const dateRegex = /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})|aujourd'hui|demain|hier/gi;
    const dateMatches = message.match(dateRegex);
    if (dateMatches) {
      entities.date = dateMatches[0];
    }
    
    // Numéros de compte
    const accountRegex = /\b\d{10,20}\b/g;
    const accountMatches = message.match(accountRegex);
    if (accountMatches) {
      entities.accountNumber = accountMatches[0];
    }
    
    // Noms de personnes (patterns simples)
    const nameRegex = /\b[A-Z][a-z]+\s+[A-Z][a-z]+\b/g;
    const nameMatches = message.match(nameRegex);
    if (nameMatches) {
      entities.personName = nameMatches[0];
    }
    
    return entities;
  }

  // Analyse de sentiment
  private analyzeSentiment(message: string, language: 'fr' | 'ar' | 'en'): 'positive' | 'neutral' | 'negative' {
    const sentimentWords = {
      fr: {
        positive: ['merci', 'excellent', 'parfait', 'super', 'génial', 'content', 'satisfait'],
        negative: ['problème', 'erreur', 'bug', 'nul', 'mauvais', 'déçu', 'frustré', 'colère']
      },
      en: {
        positive: ['thank', 'excellent', 'perfect', 'great', 'awesome', 'happy', 'satisfied'],
        negative: ['problem', 'error', 'bug', 'bad', 'terrible', 'disappointed', 'frustrated', 'angry']
      },
      ar: {
        positive: ['شكرا', 'ممتاز', 'رائع', 'جيد', 'مسرور', 'راضي'],
        negative: ['مشكلة', 'خطأ', 'سيء', 'محبط', 'غاضب', 'مستاء']
      }
    };
    
    const words = sentimentWords[language];
    const positiveCount = words.positive.filter(word => 
      message.toLowerCase().includes(word)
    ).length;
    const negativeCount = words.negative.filter(word => 
      message.toLowerCase().includes(word)
    ).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  // Détection d'urgence
  private detectUrgency(message: string, language: 'fr' | 'ar' | 'en'): 'low' | 'medium' | 'high' {
    const urgencyWords = {
      fr: {
        high: ['urgent', 'immédiat', 'rapidement', 'vite', 'emergency', 'critique'],
        medium: ['bientôt', 'aujourd\'hui', 'maintenant', 'important']
      },
      en: {
        high: ['urgent', 'immediate', 'quickly', 'fast', 'emergency', 'critical'],
        medium: ['soon', 'today', 'now', 'important']
      },
      ar: {
        high: ['عاجل', 'فوري', 'سريع', 'طارئ', 'حرج'],
        medium: ['قريبا', 'اليوم', 'الآن', 'مهم']
      }
    };
    
    const words = urgencyWords[language];
    
    if (words.high.some(word => message.toLowerCase().includes(word))) {
      return 'high';
    }
    if (words.medium.some(word => message.toLowerCase().includes(word))) {
      return 'medium';
    }
    
    return 'low';
  }

  // Génération de réponse intelligente
  private async generateResponse(nlpResult: any, context: ChatContext): Promise<BotResponse> {
    const { intent, entities, sentiment, urgency } = nlpResult;
    const language = context.currentLanguage;
    
    // Réponses basées sur l'intention
    let responseText = this.getIntentResponse(intent.intent, language, entities);
    
    // Ajustement selon le sentiment
    if (sentiment === 'negative') {
      responseText = this.addEmpathy(responseText, language);
    }
    
    // Gestion de l'urgence
    let needsHumanAgent = false;
    if (urgency === 'high' || sentiment === 'negative') {
      needsHumanAgent = true;
    }
    
    // Génération de réponses rapides
    const quickReplies = this.generateQuickReplies(intent.intent, language);
    
    // Actions suggérées
    const actions = this.generateActions(intent.intent, entities);
    
    return {
      text: responseText,
      quickReplies,
      actions,
      needsHumanAgent,
      confidence: intent.confidence,
      language
    };
  }

  // Réponses par intention
  private getIntentResponse(intent: string, language: 'fr' | 'ar' | 'en', entities: any): string {
    const responses = {
      fr: {
        'salutation': [
          'Bonjour ! Je suis votre assistant TuniBankX. Comment puis-je vous aider aujourd\'hui ?',
          'Salut ! Ravi de vous retrouver. Que puis-je faire pour vous ?',
          'Bonjour ! Votre assistant bancaire intelligent est à votre service.'
        ],
        'demande_solde': [
          'Je peux vous aider à consulter votre solde. Veuillez vous identifier d\'abord.',
          'Pour consulter votre solde, connectez-vous à votre espace client.',
          'Votre solde actuel sera affiché après authentification.'
        ],
        'demande_credit': [
          'Excellente idée ! Je peux vous guider dans votre demande de crédit. Quel type de financement vous intéresse ?',
          'Nos conseillers peuvent étudier votre dossier de crédit. Souhaitez-vous prendre rendez-vous ?',
          'TuniBankX propose plusieurs solutions de crédit adaptées à vos besoins.'
        ],
        'probleme_carte': [
          'Je comprends votre préoccupation. Pouvez-vous me décrire le problème avec votre carte ?',
          'Pas de panique ! Je vais vous aider à résoudre ce problème de carte.',
          'Pour votre sécurité, je vais vous mettre en relation avec notre service cartes.'
        ],
        'unknown': [
          'Je ne suis pas sûr de comprendre. Pouvez-vous reformuler votre question ?',
          'Pourriez-vous être plus précis ? Je suis là pour vous aider.',
          'Je n\'ai pas bien saisi. Essayez de me poser votre question différemment.'
        ]
      },
      en: {
        'greeting': [
          'Hello! I\'m your TuniBankX assistant. How can I help you today?',
          'Hi! Great to see you again. What can I do for you?',
          'Hello! Your intelligent banking assistant is at your service.'
        ],
        'balance_inquiry': [
          'I can help you check your balance. Please authenticate first.',
          'To view your balance, please log into your client area.',
          'Your current balance will be displayed after authentication.'
        ],
        'credit_request': [
          'Great idea! I can guide you through your credit application. What type of financing interests you?',
          'Our advisors can review your credit file. Would you like to schedule an appointment?',
          'TuniBankX offers several credit solutions tailored to your needs.'
        ],
        'unknown': [
          'I\'m not sure I understand. Could you rephrase your question?',
          'Could you be more specific? I\'m here to help.',
          'I didn\'t quite catch that. Try asking your question differently.'
        ]
      },
      ar: {
        'تحية': [
          'مرحبا! أنا مساعدك في تونيبانك إكس. كيف يمكنني مساعدتك اليوم؟',
          'أهلا! سعيد برؤيتك مرة أخرى. ماذا يمكنني أن أفعل لك؟',
          'مرحبا! مساعدك المصرفي الذكي في خدمتك.'
        ],
        'استعلام_رصيد': [
          'يمكنني مساعدتك في الاستعلام عن رصيدك. يرجى تسجيل الدخول أولا.',
          'لعرض رصيدك، يرجى الدخول إلى منطقة العميل.',
          'سيتم عرض رصيدك الحالي بعد المصادقة.'
        ],
        'طلب_قرض': [
          'فكرة ممتازة! يمكنني إرشادك في طلب القرض. ما نوع التمويل الذي يهمك؟',
          'يمكن لمستشارينا دراسة ملف القرض الخاص بك. هل تريد حجز موعد؟',
          'تونيبانك إكس يقدم عدة حلول ائتمانية مناسبة لاحتياجاتك.'
        ],
        'unknown': [
          'لست متأكدا من فهمي. هل يمكنك إعادة صياغة سؤالك؟',
          'هل يمكنك أن تكون أكثر تحديدا؟ أنا هنا لمساعدتك.',
          'لم أفهم جيدا. حاول طرح سؤالك بطريقة مختلفة.'
        ]
      }
    };
    
    const intentResponses = responses[language][intent] || responses[language]['unknown'];
    return intentResponses[Math.floor(Math.random() * intentResponses.length)];
  }

  // Ajout d'empathie pour sentiments négatifs
  private addEmpathy(response: string, language: 'fr' | 'ar' | 'en'): string {
    const empathyPhrases = {
      fr: ['Je comprends votre frustration. ', 'Je suis désolé pour ce désagrément. ', 'Je vais faire de mon mieux pour vous aider. '],
      en: ['I understand your frustration. ', 'I\'m sorry for this inconvenience. ', 'I\'ll do my best to help you. '],
      ar: ['أفهم إحباطك. ', 'أعتذر عن هذا الإزعاج. ', 'سأبذل قصارى جهدي لمساعدتك. ']
    };
    
    const phrase = empathyPhrases[language][Math.floor(Math.random() * empathyPhrases[language].length)];
    return phrase + response;
  }

  // Génération de réponses rapides
  private generateQuickReplies(intent: string, language: 'fr' | 'ar' | 'en'): string[] {
    const quickReplies = {
      fr: {
        'salutation': ['Consulter mon solde', 'Demander un crédit', 'Prendre RDV', 'Aide'],
        'demande_credit': ['Crédit immobilier', 'Crédit auto', 'Crédit personnel', 'Autre'],
        'probleme_carte': ['Carte bloquée', 'Carte perdue', 'Transaction refusée', 'Autre problème']
      },
      en: {
        'greeting': ['Check balance', 'Apply for credit', 'Book appointment', 'Help'],
        'credit_request': ['Home loan', 'Car loan', 'Personal loan', 'Other'],
        'card_issue': ['Blocked card', 'Lost card', 'Declined transaction', 'Other issue']
      },
      ar: {
        'تحية': ['استعلام الرصيد', 'طلب قرض', 'حجز موعد', 'مساعدة'],
        'طلب_قرض': ['قرض عقاري', 'قرض سيارة', 'قرض شخصي', 'أخرى'],
        'مشكلة_بطاقة': ['بطاقة محجوبة', 'بطاقة مفقودة', 'معاملة مرفوضة', 'مشكلة أخرى']
      }
    };
    
    return quickReplies[language][intent] || [];
  }

  // Génération d'actions
  private generateActions(intent: string, entities: any): string[] {
    const actions: string[] = [];
    
    switch (intent) {
      case 'demande_solde':
      case 'balance_inquiry':
      case 'استعلام_رصيد':
        actions.push('show_balance', 'authenticate_user');
        break;
      case 'demande_credit':
      case 'credit_request':
      case 'طلب_قرض':
        actions.push('start_credit_application', 'show_credit_calculator');
        break;
      case 'probleme_carte':
      case 'card_issue':
      case 'مشكلة_بطاقة':
        actions.push('block_card', 'contact_support');
        break;
    }
    
    return actions;
  }

  // Gestion du contexte
  private getOrCreateContext(userId: string, language: 'fr' | 'ar' | 'en'): ChatContext {
    if (!this.contexts.has(userId)) {
      this.contexts.set(userId, {
        userId,
        sessionId: this.generateSessionId(),
        currentLanguage: language,
        conversationHistory: [],
        entities: {},
        mood: 'neutral'
      });
    }
    return this.contexts.get(userId)!;
  }

  private updateContext(context: ChatContext, message: string, nlpResult: any): void {
    // Mise à jour des entités
    Object.assign(context.entities, nlpResult.entities);
    
    // Mise à jour de l'humeur
    context.mood = nlpResult.sentiment;
    
    // Mise à jour de l'intention courante
    context.currentIntent = nlpResult.intent.intent;
  }

  private saveToHistory(context: ChatContext, userMessage: string, botResponse: BotResponse): void {
    const timestamp = new Date();
    
    // Message utilisateur
    context.conversationHistory.push({
      id: this.generateMessageId(),
      text: userMessage,
      sender: 'user',
      timestamp,
      language: context.currentLanguage
    });
    
    // Réponse du bot
    context.conversationHistory.push({
      id: this.generateMessageId(),
      text: botResponse.text,
      sender: 'bot',
      timestamp,
      language: context.currentLanguage
    });
    
    // Garder seulement les 50 derniers messages
    if (context.conversationHistory.length > 50) {
      context.conversationHistory = context.conversationHistory.slice(-50);
    }
  }

  // Méthodes utilitaires
  private normalizeText(text: string): string {
    return text.toLowerCase()
      .replace(/[^\w\s\u0600-\u06FF]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateMessageId(): string {
    return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private initializeNLP(): void {
    // Initialisation des modèles NLP
    this.intents = {};
    this.entities = {};
    this.responses = {};
  }

  private loadBankingKnowledge(): void {
    // Chargement de la base de connaissances bancaires
    // Implémentation avec vraies données bancaires
  }

  // API publiques
  public getConversationHistory(userId: string): ChatMessage[] {
    const context = this.contexts.get(userId);
    return context ? context.conversationHistory : [];
  }

  public clearContext(userId: string): void {
    this.contexts.delete(userId);
  }

  public setUserProfile(userId: string, profile: any): void {
    const context = this.getOrCreateContext(userId, 'fr');
    context.userProfile = profile;
  }
}
