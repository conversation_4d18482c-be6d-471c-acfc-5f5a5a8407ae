// Service d'IA de Scoring Prédictif Avancé pour TuniBankX
// Système révolutionnaire de machine learning pour l'évaluation des risques

export interface ScoringFactors {
  // Facteurs financiers
  revenuMensuel: number;
  chargesMensuelles: number;
  patrimoine: number;
  autresCredits: number;
  tauxEndettement: number;
  
  // Facteurs professionnels
  ancienneteEmploi: number;
  stabiliteEmploi: number;
  secteurActivite: string;
  typeContrat: string;
  
  // Facteurs comportementaux
  historiqueTransactions: number[];
  frequenceOperations: number;
  montantMoyenTransactions: number;
  regularitePaiements: number;
  
  // Facteurs sociodémographiques
  age: number;
  situationFamiliale: string;
  nombreEnfants: number;
  niveauEducation: string;
  
  // Facteurs géographiques
  region: string;
  typeLogement: string;
  ancienneteAdresse: number;
  
  // Facteurs macroéconomiques
  inflationRate: number;
  unemploymentRate: number;
  gdpGrowth: number;
}

export interface ScoringResult {
  score: number;
  niveau: 'excellent' | 'bon' | 'moyen' | 'faible' | 'critique';
  probabiliteDefaut: number;
  montantMaxRecommande: number;
  tauxInteretRecommande: number;
  dureeMaxRecommandee: number;
  facteursPrincipaux: string[];
  recommandations: string[];
  alertes: string[];
  confiance: number;
  tendance: 'amelioration' | 'stable' | 'deterioration';
}

export interface PredictiveAnalysis {
  evolutionScore6Mois: number[];
  probabiliteDefaut12Mois: number;
  facteurRisqueEmergent: string[];
  opportunitesCommerciales: string[];
  actionsPrevention: string[];
}

export class AIScoring {
  private static instance: AIScoring;
  private modelWeights: { [key: string]: number };
  private historicalData: any[];

  constructor() {
    // Poids du modèle d'IA basés sur l'analyse de données bancaires tunisiennes
    this.modelWeights = {
      // Facteurs financiers (40%)
      revenuMensuel: 0.15,
      tauxEndettement: 0.12,
      patrimoine: 0.08,
      stabiliteFinanciere: 0.05,
      
      // Facteurs comportementaux (25%)
      historiqueCredit: 0.10,
      regularitePaiements: 0.08,
      comportementBancaire: 0.07,
      
      // Facteurs professionnels (20%)
      stabiliteEmploi: 0.08,
      secteurActivite: 0.07,
      ancienneteEmploi: 0.05,
      
      // Facteurs sociodémographiques (10%)
      age: 0.04,
      situationFamiliale: 0.03,
      education: 0.03,
      
      // Facteurs macroéconomiques (5%)
      contexteEconomique: 0.03,
      risquePays: 0.02
    };

    this.historicalData = this.generateHistoricalData();
  }

  static getInstance(): AIScoring {
    if (!AIScoring.instance) {
      AIScoring.instance = new AIScoring();
    }
    return AIScoring.instance;
  }

  // Calcul du score principal avec IA
  calculateAdvancedScore(factors: ScoringFactors): ScoringResult {
    // Normalisation des facteurs
    const normalizedFactors = this.normalizeFactors(factors);
    
    // Calcul du score de base
    let baseScore = this.calculateBaseScore(normalizedFactors);
    
    // Application des ajustements IA
    const aiAdjustments = this.applyAIAdjustments(normalizedFactors);
    const finalScore = Math.max(0, Math.min(100, baseScore + aiAdjustments));
    
    // Analyse comportementale avancée
    const behavioralAnalysis = this.analyzeBehavioralPatterns(factors);
    
    // Calcul de la probabilité de défaut
    const probabiliteDefaut = this.calculateDefaultProbability(finalScore, factors);
    
    // Recommandations personnalisées
    const recommendations = this.generateRecommendations(finalScore, factors);
    
    // Détection d'alertes
    const alertes = this.detectAlerts(factors);
    
    // Calcul de la confiance du modèle
    const confiance = this.calculateModelConfidence(factors);
    
    // Analyse de tendance
    const tendance = this.analyzeTrend(factors);

    return {
      score: Math.round(finalScore),
      niveau: this.getScoreLevel(finalScore),
      probabiliteDefaut: Math.round(probabiliteDefaut * 100) / 100,
      montantMaxRecommande: this.calculateMaxAmount(finalScore, factors),
      tauxInteretRecommande: this.calculateRecommendedRate(finalScore, factors),
      dureeMaxRecommandee: this.calculateMaxDuration(finalScore, factors),
      facteursPrincipaux: this.getMainFactors(normalizedFactors),
      recommandations: recommendations,
      alertes: alertes,
      confiance: Math.round(confiance * 100),
      tendance: tendance
    };
  }

  // Analyse prédictive avancée
  generatePredictiveAnalysis(factors: ScoringFactors): PredictiveAnalysis {
    const currentScore = this.calculateAdvancedScore(factors).score;
    
    // Simulation de l'évolution sur 6 mois
    const evolutionScore6Mois = this.simulateScoreEvolution(factors, 6);
    
    // Prédiction de défaut à 12 mois
    const probabiliteDefaut12Mois = this.predictDefaultProbability12Months(factors);
    
    // Détection de facteurs de risque émergents
    const facteurRisqueEmergent = this.detectEmergingRiskFactors(factors);
    
    // Identification d'opportunités commerciales
    const opportunitesCommerciales = this.identifyBusinessOpportunities(factors);
    
    // Actions de prévention recommandées
    const actionsPrevention = this.recommendPreventiveActions(factors);

    return {
      evolutionScore6Mois,
      probabiliteDefaut12Mois,
      facteurRisqueEmergent,
      opportunitesCommerciales,
      actionsPrevention
    };
  }

  private normalizeFactors(factors: ScoringFactors): { [key: string]: number } {
    return {
      revenuNormalise: Math.min(1, factors.revenuMensuel / 5000),
      endettementNormalise: Math.max(0, 1 - (factors.tauxEndettement / 100)),
      patrimoineNormalise: Math.min(1, factors.patrimoine / 200000),
      stabiliteEmploiNormalise: Math.min(1, factors.ancienneteEmploi / 10),
      ageNormalise: this.normalizeAge(factors.age),
      comportementNormalise: factors.regularitePaiements / 100
    };
  }

  private calculateBaseScore(normalizedFactors: { [key: string]: number }): number {
    let score = 0;
    
    // Application des poids du modèle
    score += normalizedFactors.revenuNormalise * this.modelWeights.revenuMensuel * 100;
    score += normalizedFactors.endettementNormalise * this.modelWeights.tauxEndettement * 100;
    score += normalizedFactors.patrimoineNormalise * this.modelWeights.patrimoine * 100;
    score += normalizedFactors.stabiliteEmploiNormalise * this.modelWeights.stabiliteEmploi * 100;
    score += normalizedFactors.ageNormalise * this.modelWeights.age * 100;
    score += normalizedFactors.comportementNormalise * this.modelWeights.regularitePaiements * 100;
    
    return score;
  }

  private applyAIAdjustments(normalizedFactors: { [key: string]: number }): number {
    let adjustments = 0;
    
    // Ajustements basés sur l'apprentissage automatique
    // Bonus pour la cohérence des données
    if (this.checkDataConsistency(normalizedFactors)) {
      adjustments += 5;
    }
    
    // Ajustement pour les tendances du marché
    adjustments += this.getMarketTrendAdjustment();
    
    // Ajustement saisonnier
    adjustments += this.getSeasonalAdjustment();
    
    return adjustments;
  }

  private analyzeBehavioralPatterns(factors: ScoringFactors): any {
    // Analyse des patterns comportementaux avec IA
    return {
      stabiliteComportement: factors.regularitePaiements > 80,
      tendanceDepenses: this.analyzeTrendSpending(factors.historiqueTransactions),
      risqueComportemental: this.calculateBehavioralRisk(factors)
    };
  }

  private calculateDefaultProbability(score: number, factors: ScoringFactors): number {
    // Modèle logistique pour la probabilité de défaut
    const logit = -4.5 + (100 - score) * 0.08 + factors.tauxEndettement * 0.02;
    return 1 / (1 + Math.exp(-logit));
  }

  private generateRecommendations(score: number, factors: ScoringFactors): string[] {
    const recommendations: string[] = [];
    
    if (score >= 80) {
      recommendations.push("Client excellent - Proposer des produits premium");
      recommendations.push("Augmenter les plafonds de crédit");
      recommendations.push("Offrir des taux préférentiels");
    } else if (score >= 60) {
      recommendations.push("Client bon - Maintenir la relation");
      recommendations.push("Surveiller l'évolution du profil");
    } else if (score >= 40) {
      recommendations.push("Client à risque modéré - Conditions strictes");
      recommendations.push("Demander des garanties supplémentaires");
      recommendations.push("Réduire les montants accordés");
    } else {
      recommendations.push("Client à haut risque - Éviter les nouveaux crédits");
      recommendations.push("Renforcer le suivi");
      recommendations.push("Proposer un accompagnement financier");
    }
    
    // Recommandations spécifiques basées sur les facteurs
    if (factors.tauxEndettement > 70) {
      recommendations.push("Taux d'endettement élevé - Proposer un regroupement de crédits");
    }
    
    if (factors.ancienneteEmploi < 2) {
      recommendations.push("Ancienneté faible - Demander des justificatifs supplémentaires");
    }
    
    return recommendations;
  }

  private detectAlerts(factors: ScoringFactors): string[] {
    const alertes: string[] = [];
    
    if (factors.tauxEndettement > 80) {
      alertes.push("🚨 Taux d'endettement critique");
    }
    
    if (factors.regularitePaiements < 50) {
      alertes.push("⚠️ Historique de paiements irréguliers");
    }
    
    if (factors.ancienneteEmploi < 1) {
      alertes.push("⚠️ Stabilité professionnelle faible");
    }
    
    if (factors.autresCredits > factors.revenuMensuel * 3) {
      alertes.push("🚨 Surendettement potentiel");
    }
    
    return alertes;
  }

  private getScoreLevel(score: number): 'excellent' | 'bon' | 'moyen' | 'faible' | 'critique' {
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'bon';
    if (score >= 55) return 'moyen';
    if (score >= 40) return 'faible';
    return 'critique';
  }

  private calculateMaxAmount(score: number, factors: ScoringFactors): number {
    const baseAmount = factors.revenuMensuel * 36; // 3 ans de revenus
    const scoreMultiplier = score / 100;
    const endettementAdjustment = Math.max(0.3, 1 - factors.tauxEndettement / 100);
    
    return Math.round(baseAmount * scoreMultiplier * endettementAdjustment);
  }

  private calculateRecommendedRate(score: number, factors: ScoringFactors): number {
    const baseRate = 8.0; // Taux de base BCT + marge
    const riskPremium = Math.max(0, (100 - score) / 100 * 5);
    
    return Math.round((baseRate + riskPremium) * 100) / 100;
  }

  private calculateMaxDuration(score: number, factors: ScoringFactors): number {
    if (score >= 80) return 300; // 25 ans
    if (score >= 70) return 240; // 20 ans
    if (score >= 60) return 180; // 15 ans
    if (score >= 50) return 120; // 10 ans
    return 84; // 7 ans
  }

  private getMainFactors(normalizedFactors: { [key: string]: number }): string[] {
    const factors = Object.entries(normalizedFactors)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([key]) => this.translateFactorName(key));
    
    return factors;
  }

  private translateFactorName(key: string): string {
    const translations: { [key: string]: string } = {
      'revenuNormalise': 'Niveau de revenus',
      'endettementNormalise': 'Capacité d\'endettement',
      'patrimoineNormalise': 'Patrimoine personnel',
      'stabiliteEmploiNormalise': 'Stabilité professionnelle',
      'ageNormalise': 'Profil démographique',
      'comportementNormalise': 'Comportement de paiement'
    };
    
    return translations[key] || key;
  }

  private simulateScoreEvolution(factors: ScoringFactors, months: number): number[] {
    const evolution: number[] = [];
    const currentScore = this.calculateAdvancedScore(factors).score;
    
    for (let i = 1; i <= months; i++) {
      // Simulation basée sur les tendances historiques
      const trend = Math.random() * 4 - 2; // Variation de -2 à +2
      const seasonalEffect = Math.sin(i * Math.PI / 6) * 1.5;
      const newScore = Math.max(0, Math.min(100, currentScore + trend + seasonalEffect));
      evolution.push(Math.round(newScore));
    }
    
    return evolution;
  }

  private predictDefaultProbability12Months(factors: ScoringFactors): number {
    const currentProb = this.calculateDefaultProbability(
      this.calculateAdvancedScore(factors).score, 
      factors
    );
    
    // Ajustement pour la période de 12 mois
    const timeAdjustment = 1.2; // Augmentation du risque dans le temps
    
    return Math.min(1, currentProb * timeAdjustment);
  }

  private detectEmergingRiskFactors(factors: ScoringFactors): string[] {
    const risks: string[] = [];
    
    if (factors.secteurActivite === 'tourisme' || factors.secteurActivite === 'restauration') {
      risks.push("Secteur sensible aux crises économiques");
    }
    
    if (factors.typeContrat === 'cdd') {
      risks.push("Précarité de l'emploi");
    }
    
    if (factors.region === 'intérieur') {
      risks.push("Zone géographique à risque économique");
    }
    
    return risks;
  }

  private identifyBusinessOpportunities(factors: ScoringFactors): string[] {
    const opportunities: string[] = [];
    
    if (factors.revenuMensuel > 3000 && factors.tauxEndettement < 30) {
      opportunities.push("Prospect pour crédit immobilier premium");
    }
    
    if (factors.age < 35 && factors.stabiliteEmploi > 0.8) {
      opportunities.push("Cible pour produits d'épargne jeunes");
    }
    
    if (factors.patrimoine > 100000) {
      opportunities.push("Client potentiel pour gestion de patrimoine");
    }
    
    return opportunities;
  }

  private recommendPreventiveActions(factors: ScoringFactors): string[] {
    const actions: string[] = [];
    
    if (factors.tauxEndettement > 60) {
      actions.push("Proposer un conseil en gestion budgétaire");
    }
    
    if (factors.regularitePaiements < 70) {
      actions.push("Mettre en place des rappels automatiques");
    }
    
    if (factors.ancienneteEmploi < 2) {
      actions.push("Demander une assurance perte d'emploi");
    }
    
    return actions;
  }

  // Méthodes utilitaires
  private normalizeAge(age: number): number {
    // Courbe optimale entre 30-50 ans
    if (age >= 30 && age <= 50) return 1;
    if (age < 30) return 0.7 + (age - 18) / 12 * 0.3;
    return Math.max(0.5, 1 - (age - 50) / 20 * 0.5);
  }

  private checkDataConsistency(factors: { [key: string]: number }): boolean {
    // Vérification de la cohérence des données
    return Object.values(factors).every(value => value >= 0 && value <= 1);
  }

  private getMarketTrendAdjustment(): number {
    // Ajustement basé sur les tendances du marché
    const currentDate = new Date();
    const month = currentDate.getMonth();
    
    // Ajustements saisonniers pour la Tunisie
    if (month >= 5 && month <= 8) return -1; // Été - période difficile
    if (month >= 10 && month <= 12) return 1; // Fin d'année - période favorable
    
    return 0;
  }

  private getSeasonalAdjustment(): number {
    // Ajustements saisonniers spécifiques à la Tunisie
    const currentDate = new Date();
    const month = currentDate.getMonth();
    
    if (month === 6 || month === 7) return -2; // Ramadan/Été
    if (month === 11) return 2; // Fin d'année
    
    return 0;
  }

  private analyzeTrendSpending(transactions: number[]): string {
    if (transactions.length < 3) return 'stable';
    
    const recent = transactions.slice(-3);
    const average = recent.reduce((a, b) => a + b, 0) / recent.length;
    const previous = transactions.slice(-6, -3);
    const previousAverage = previous.reduce((a, b) => a + b, 0) / previous.length;
    
    if (average > previousAverage * 1.1) return 'croissante';
    if (average < previousAverage * 0.9) return 'décroissante';
    return 'stable';
  }

  private calculateBehavioralRisk(factors: ScoringFactors): number {
    let risk = 0;
    
    if (factors.frequenceOperations > 50) risk += 0.1; // Trop d'opérations
    if (factors.montantMoyenTransactions > factors.revenuMensuel * 0.5) risk += 0.2;
    if (factors.regularitePaiements < 80) risk += 0.3;
    
    return Math.min(1, risk);
  }

  private calculateModelConfidence(factors: ScoringFactors): number {
    let confidence = 0.8; // Base de confiance
    
    // Réduction si données manquantes ou incohérentes
    if (factors.ancienneteEmploi === 0) confidence -= 0.1;
    if (factors.patrimoine === 0) confidence -= 0.05;
    if (factors.historiqueTransactions.length < 6) confidence -= 0.1;
    
    return Math.max(0.5, confidence);
  }

  private analyzeTrend(factors: ScoringFactors): 'amelioration' | 'stable' | 'deterioration' {
    // Analyse de tendance basée sur l'historique
    const trendScore = factors.regularitePaiements;
    
    if (trendScore > 85) return 'amelioration';
    if (trendScore < 60) return 'deterioration';
    return 'stable';
  }

  private generateHistoricalData(): any[] {
    // Génération de données historiques pour l'entraînement du modèle
    return []; // Implémentation complète avec vraies données
  }
}
