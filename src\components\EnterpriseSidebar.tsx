import React, { useState } from 'react';
import { 
  BarChart3, Users, CreditCard, Brain, FileText, Settings,
  TrendingUp, Shield, Globe, Database, Zap, Award,
  ChevronRight, ChevronDown, Home, PieChart, Target,
  Briefcase, Calculator, BookOpen, AlertTriangle,
  CheckCircle, Clock, DollarSign, Activity, Layers
} from 'lucide-react';

interface EnterpriseSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const EnterpriseSidebar: React.FC<EnterpriseSidebarProps> = ({ activeTab, onTabChange }) => {
  const [expandedSections, setExpandedSections] = useState<string[]>(['banking', 'analytics']);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const menuSections = [
    {
      id: 'overview',
      title: 'Vue d\'ensemble',
      icon: Home,
      items: [
        { id: 'dashboard', label: 'Dashboard Exécutif', icon: BarChart3, badge: 'Live' },
        { id: 'performance', label: 'Performance Globale', icon: TrendingUp, badge: '+12%' },
        { id: 'alerts', label: 'Alertes & Monitoring', icon: AlertTriangle, badge: '3' }
      ]
    },
    {
      id: 'banking',
      title: 'Services Bancaires',
      icon: Briefcase,
      items: [
        { id: 'clients', label: 'Gestion Clients', icon: Users, badge: '1,247' },
        { id: 'credits', label: 'Portefeuille Crédits', icon: CreditCard, badge: '€2.4M' },
        { id: 'accounts', label: 'Comptes & Dépôts', icon: DollarSign, badge: 'New' },
        { id: 'transactions', label: 'Transactions', icon: Activity, badge: 'Live' }
      ]
    },
    {
      id: 'analytics',
      title: 'Intelligence Artificielle',
      icon: Brain,
      items: [
        { id: 'analytics', label: 'Technologies IA', icon: Zap, badge: 'AI' },
        { id: 'scoring', label: 'Scoring Prédictif', icon: Target, badge: '96%' },
        { id: 'risk', label: 'Analyse des Risques', icon: Shield, badge: 'ML' },
        { id: 'forecasting', label: 'Prévisions Marché', icon: PieChart, badge: 'Beta' }
      ]
    },
    {
      id: 'operations',
      title: 'Opérations',
      icon: Layers,
      items: [
        { id: 'documents', label: 'Génération Documents', icon: FileText, badge: 'Auto' },
        { id: 'compliance', label: 'Conformité BCT', icon: CheckCircle, badge: 'OK' },
        { id: 'blockchain', label: 'Blockchain Audit', icon: Database, badge: 'Secure' },
        { id: 'reports', label: 'Rapports Exécutifs', icon: BookOpen, badge: 'PDF' }
      ]
    },
    {
      id: 'administration',
      title: 'Administration',
      icon: Settings,
      items: [
        { id: 'users', label: 'Gestion Utilisateurs', icon: Users, badge: null },
        { id: 'security', label: 'Sécurité & Accès', icon: Shield, badge: null },
        { id: 'integration', label: 'Intégrations API', icon: Globe, badge: null },
        { id: 'settings', label: 'Paramètres Système', icon: Settings, badge: null }
      ]
    }
  ];

  const getBadgeColor = (badge: string | null) => {
    if (!badge) return '';
    switch (badge) {
      case 'Live': return 'bg-green-100 text-green-700 border-green-200';
      case 'AI': case 'ML': return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'New': case 'Beta': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Auto': case 'Secure': return 'bg-indigo-100 text-indigo-700 border-indigo-200';
      case 'OK': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <aside className="w-72 bg-white border-r border-gray-200 shadow-sm flex flex-col h-screen">
      {/* Header Sidebar */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center">
            <Award className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-gray-900">Centre de Contrôle</h2>
            <p className="text-sm text-gray-500">Plateforme Enterprise</p>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 overflow-y-auto py-4">
        <div className="px-4 space-y-2">
          {menuSections.map((section) => {
            const SectionIcon = section.icon;
            const isExpanded = expandedSections.includes(section.id);
            
            return (
              <div key={section.id} className="space-y-1">
                {/* Section Header */}
                <button
                  onClick={() => toggleSection(section.id)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors group"
                >
                  <div className="flex items-center space-x-3">
                    <SectionIcon className="w-5 h-5 text-gray-400 group-hover:text-gray-600" />
                    <span>{section.title}</span>
                  </div>
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  )}
                </button>

                {/* Section Items */}
                {isExpanded && (
                  <div className="ml-6 space-y-1">
                    {section.items.map((item) => {
                      const ItemIcon = item.icon;
                      const isActive = activeTab === item.id;
                      
                      return (
                        <button
                          key={item.id}
                          onClick={() => onTabChange(item.id)}
                          className={`w-full flex items-center justify-between px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group ${
                            isActive
                              ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <ItemIcon className={`w-4 h-4 ${
                              isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'
                            }`} />
                            <span className="font-medium">{item.label}</span>
                          </div>
                          
                          {item.badge && (
                            <span className={`px-2 py-0.5 text-xs font-medium rounded-full border ${getBadgeColor(item.badge)}`}>
                              {item.badge}
                            </span>
                          )}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </nav>

      {/* Footer avec Métriques */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="space-y-3">
          {/* Statut Système */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Statut Système</span>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-600 font-medium">Opérationnel</span>
            </div>
          </div>
          
          {/* Métriques Rapides */}
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-white p-3 rounded-lg border border-gray-200">
              <div className="text-lg font-bold text-gray-900">99.9%</div>
              <div className="text-xs text-gray-500">Disponibilité</div>
            </div>
            <div className="bg-white p-3 rounded-lg border border-gray-200">
              <div className="text-lg font-bold text-blue-600">&lt; 2s</div>
              <div className="text-xs text-gray-500">Temps Réponse</div>
            </div>
          </div>
          
          {/* Version */}
          <div className="text-center">
            <span className="text-xs text-gray-400">TuniBankX Enterprise v2.4.1</span>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default EnterpriseSidebar;
