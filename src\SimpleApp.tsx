import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Composant de test simple pour l'authentification
const SimpleLogin: React.FC = () => {
  const { login, isAuthenticated, user, error } = useAuth();

  const handleLogin = async () => {
    try {
      const success = await login('directeur.credit', '1234');
      console.log('Login result:', success);
    } catch (err) {
      console.error('Login error:', err);
    }
  };

  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-green-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
          <h1 className="text-2xl font-bold text-green-800 mb-4">
            ✅ Connexion Réussie !
          </h1>
          <p className="text-gray-700 mb-2">
            Bienvenue, {user?.prenom} {user?.nom}
          </p>
          <p className="text-sm text-gray-500">
            Rôle: {user?.role}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-blue-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md">
        <h1 className="text-2xl font-bold text-blue-800 mb-4">
          TuniBankX - Test Login
        </h1>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <div className="mb-4">
          <p className="text-gray-600 mb-2">Compte de test :</p>
          <p className="text-sm text-gray-500">
            Utilisateur: directeur.credit<br />
            PIN: 1234
          </p>
        </div>
        
        <button
          onClick={handleLogin}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
        >
          Se connecter
        </button>
      </div>
    </div>
  );
};

// Composant principal simplifié
const SimpleApp: React.FC = () => {
  return (
    <AuthProvider>
      <SimpleLogin />
    </AuthProvider>
  );
};

export default SimpleApp;
